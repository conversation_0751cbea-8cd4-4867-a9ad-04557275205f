import { Extension } from '@tiptap/core'

export interface FooterOptions {
  documentTitle: string
  showPageNumbers: boolean
  leftContent?: string
  centerContent?: string
  rightContent?: string
  customTemplate?: string
}

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    footer: {
      /**
       * Mettre à jour le pied de page
       */
      updateFooter: (options: Partial<FooterOptions>) => ReturnType
    }
  }
}

export const FooterExtension = Extension.create<FooterOptions>({
  name: 'footer',

  addOptions() {
    return {
      documentTitle: 'Document',
      showPageNumbers: true,
      leftContent: '',
      centerContent: '',
      rightContent: '',
      customTemplate: '',
    }
  },

  addGlobalAttributes() {
    return [
      {
        types: ['doc'],
        attributes: {
          footerConfig: {
            default: {
              documentTitle: this.options.documentTitle,
              showPageNumbers: this.options.showPageNumbers,
              leftContent: this.options.leftContent,
              centerContent: this.options.centerContent,
              rightContent: this.options.rightContent,
              customTemplate: this.options.customTemplate,
            },
            parseHTML: () => null,
            renderHTML: () => null,
          },
        },
      },
    ]
  },

  addCommands() {
    return {
      updateFooter:
        (options) =>
        ({ tr, state }) => {
          const { doc } = state
          const currentFooterConfig = doc.attrs.footerConfig || {}
          
          const newFooterConfig = {
            ...currentFooterConfig,
            ...options,
          }

          tr.setDocAttribute('footerConfig', newFooterConfig)
          
          return true
        },
    }
  },

  onCreate() {
    // Initialiser la configuration du pied de page
    this.editor.commands.updateFooter({
      documentTitle: this.options.documentTitle,
      showPageNumbers: this.options.showPageNumbers,
      leftContent: this.options.leftContent,
      centerContent: this.options.centerContent,
      rightContent: this.options.rightContent,
      customTemplate: this.options.customTemplate,
    })
  },

  addStorage() {
    return {
      generateFooter: (pageNumber: number, totalPages: number) => {
        const footerConfig = this.editor.state.doc.attrs.footerConfig || this.options
        
        if (footerConfig.customTemplate) {
          return footerConfig.customTemplate
            .replace('{pageNumber}', pageNumber.toString())
            .replace('{totalPages}', totalPages.toString())
            .replace('{documentTitle}', footerConfig.documentTitle)
        }

        const leftContent = footerConfig.leftContent || footerConfig.documentTitle
        const centerContent = footerConfig.centerContent || ''
        const rightContent = footerConfig.rightContent || 
          (footerConfig.showPageNumbers ? `Page ${pageNumber} / ${totalPages}` : '')

        return `
          <div class="footer-content">
            <div class="footer-left">${leftContent}</div>
            <div class="footer-center">${centerContent}</div>
            <div class="footer-right">${rightContent}</div>
          </div>
        `
      },
    }
  },
})

/* Styles pour la pagination type Word */

/* Container principal avec pagination */
.paginated-editor {
  background: #f5f5f5;
  min-height: 100vh;
  padding: 20px;
  position: relative;
}

/* Zone de contenu paginé */
.paginated-content {
  max-width: 850px; /* Largeur A4 + marges */
  margin: 0 auto;
  position: relative;
}

/* Pages individuelles */
.document-page {
  width: 794px; /* A4 width at 96 DPI */
  min-height: 1123px; /* A4 height at 96 DPI */
  background: white;
  margin-bottom: 20px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  padding: 30px;
  box-sizing: border-box;
  page-break-after: always;
}

/* Page de garde spéciale */
.document-page.cover-page-wrapper {
  padding: 0; /* Pas de padding pour la page de garde */
  border: none; /* Pas de bordure pour éviter la page blanche */
  display: none; /* Masquer complètement cette page wrapper vide */
}

.document-page.cover-page-wrapper .page-content {
  padding: 0;
  min-height: 1123px;
}

/* Page vierge */
.document-page.blank-page {
  background: white;
}

.document-page.blank-page .blank-content {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: calc(1123px - 60px);
}

.document-page.blank-page .blank-page-text {
  color: #ccc;
  font-style: italic;
  font-size: 14px;
  text-align: center;
}

/* Contenu de la page (zone éditable) */
.page-content {
  min-height: calc(1123px - 60px - 60px); /* Hauteur page - marges top/bottom */
  padding-bottom: 60px; /* Espace pour le pied de page */
}

/* Pied de page */
.page-footer {
  position: absolute;
  bottom: 15px;
  left: 30px;
  right: 30px;
  height: 30px;
  border-top: 1px solid #e5e7eb;
  padding-top: 8px;
  font-size: 10pt;
  color: #666;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.document-title {
  font-weight: 500;
  color: #333;
}

.page-number {
  color: #666;
  font-size: 9pt;
}

/* Saut de page manuel */
.page-break {
  height: 1px;
  background: transparent;
  border: none;
  page-break-before: always;
  margin: 0;
  padding: 0;
  position: relative;
}

.page-break::before {
  content: "--- Saut de page ---";
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  background: #f0f0f0;
  padding: 2px 8px;
  font-size: 10px;
  color: #666;
  border: 1px dashed #ccc;
  border-radius: 3px;
  white-space: nowrap;
}

/* Séparateurs visuels entre pages */
.page-separator {
  border-top: 1px dashed #ccc;
  margin: 20px 0;
  position: relative;
}

.page-separator::before {
  content: "Page suivante";
  position: absolute;
  top: -8px;
  left: 50%;
  transform: translateX(-50%);
  background: #f5f5f5;
  padding: 0 8px;
  font-size: 10px;
  color: #999;
}

/* Styles pour l'éditeur dans le contexte paginé */
.paginated-editor .ProseMirror {
  outline: none;
  font-family: 'Times New Roman', serif;
  font-size: 12pt;
  line-height: 1.5;
  color: #000;
  background: transparent;
  box-shadow: none;
  padding: 0;
  margin: 0;
  width: 100%;
  min-height: auto;
}

/* Styles spéciaux pour la page de garde dans l'éditeur paginé */
.paginated-editor .ProseMirror [data-type="cover-page"] {
  width: 100%;
  min-height: 1123px;
  margin: 0;
  padding: 0;
  background: white;
  border: 3px solid #000;
  box-sizing: border-box;
  page-break-after: always;
}

.paginated-editor .ProseMirror [data-type="cover-page"] .pagedegarde {
  margin: 0;
  padding: 25px;
  background: white;
  border: none;
  width: 100%;
  min-height: calc(1123px - 50px);
  box-sizing: border-box;
}

/* Masquer la page de garde du flux normal dans l'éditeur paginé */
.paginated-editor-container [data-type="cover-page"] {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1;
}

/* Éviter les coupures de page au milieu des éléments */
.paginated-editor .ProseMirror h1,
.paginated-editor .ProseMirror h2,
.paginated-editor .ProseMirror h3,
.paginated-editor .ProseMirror h4,
.paginated-editor .ProseMirror h5,
.paginated-editor .ProseMirror h6 {
  page-break-after: avoid;
  page-break-inside: avoid;
}

.paginated-editor .ProseMirror table {
  page-break-inside: avoid;
}

.paginated-editor .ProseMirror p {
  orphans: 2;
  widows: 2;
}

/* Bouton pour insérer un saut de page */
.page-break-button {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  color: #495057;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.page-break-button:hover {
  background: #e9ecef;
  border-color: #adb5bd;
}

.page-break-button:active {
  background: #dee2e6;
}

/* Indicateur de page courante */
.page-indicator {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  z-index: 1000;
}

/* Styles d'impression optimisés */
@media print {
  .paginated-editor {
    background: white;
    padding: 0;
  }

  .paginated-content {
    max-width: none;
    margin: 0;
  }

  .document-page {
    width: 210mm;
    min-height: 297mm;
    margin: 0;
    box-shadow: none;
    padding: 20mm;
    page-break-after: always;
  }

  /* Page de garde à l'impression */
  .document-page.cover-page-wrapper {
    padding: 0;
    border: 3px solid #000;
  }

  .document-page.cover-page-wrapper .page-content {
    padding: 0;
    min-height: 297mm;
  }

  /* Page vierge à l'impression */
  .document-page.blank-page {
    padding: 20mm;
  }

  .document-page.blank-page .blank-page-text {
    display: none; /* Masquer le texte à l'impression */
  }

  .page-content {
    min-height: calc(297mm - 40mm);
    padding-bottom: 20mm;
  }

  .page-footer {
    bottom: 10mm;
    left: 20mm;
    right: 20mm;
    font-size: 9pt;
  }

  .page-break::before,
  .page-separator::before,
  .page-indicator {
    display: none;
  }

  .page-separator {
    display: none;
  }

  /* Styles spéciaux pour la page de garde à l'impression */
  [data-type="cover-page"] {
    width: 210mm !important;
    min-height: 297mm !important;
    margin: 0 !important;
    padding: 0 !important;
    border: 3px solid #000 !important;
    page-break-after: always !important;
  }

  [data-type="cover-page"] .pagedegarde {
    width: 100% !important;
    min-height: calc(297mm - 6mm) !important;
    margin: 0 !important;
    padding: 20mm !important;
    border: none !important;
    background: white !important;
  }

  @page {
    size: A4;
    margin: 0;
  }
}

/* Container pour l'éditeur paginé intégré */
.paginated-editor-container {
  flex: 1;
  overflow-y: auto;
  background: #f5f5f5;
  padding: 20px;
  position: relative;
}

.paginated-editor-container .paginated-content {
  max-width: 850px;
  margin: 0 auto;
  position: relative;
}

/* Barre d'outils de pagination */
.pagination-toolbar {
  background: #f8f9fa;
  border-bottom: 1px solid #e5e7eb;
  padding: 8px 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 14px;
}

.pagination-toolbar button {
  transition: all 0.2s ease;
}

.pagination-toolbar button:hover:not(:disabled) {
  background: #e9ecef;
  border-color: #0F766E;
}

.pagination-toolbar button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Mode responsive pour petits écrans */
@media (max-width: 900px) {
  .paginated-editor {
    padding: 10px;
  }

  .paginated-content {
    max-width: 100%;
  }

  .document-page {
    width: 100%;
    min-height: auto;
    margin-bottom: 10px;
    padding: 20px;
  }

  .page-content {
    min-height: auto;
    padding-bottom: 40px;
  }

  .paginated-editor-container {
    padding: 10px;
  }

  .pagination-toolbar {
    flex-wrap: wrap;
    gap: 8px;
    padding: 8px 12px;
  }
}

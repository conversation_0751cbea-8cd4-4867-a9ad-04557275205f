/**
 * Service de génération de page de garde HARMONIA
 * Reproduit exactement le style de la page de garde de référence
 */

export interface CoverPageData {
  lot: {
    id: number
    name: string
    code?: string
    description?: string
    current_phase?: string
    photo_url?: string
    project?: {
      id: number
      name: string
      address?: string
      photo_url?: string
    }
  }
  documentType: string
  documentIndice: string
  companies: Array<{
    id: number
    name: string
    role: string
    activity?: string
    address?: string
    phone?: string
    email?: string
    logo_url?: string | null
  }>
}

export function generateHarmoniaCoverPageHTML(data: CoverPageData): string {
  console.log('🎯 Génération page de garde - Style HARMONIA')
  console.log('📊 Données:', data)

  // Préparer les données
  const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'
  
  // Trouver les entreprises par rôle
  const moaCompany = data.companies.find(c => c.role === 'MOA')
  const otherCompanies = data.companies.filter(c => c.role !== 'MOA')

  // Titre du document
  const getDocumentTitle = (type: string): string => {
    switch (type.toUpperCase()) {
      case 'CCTP': return 'CCTP'
      case 'DPGF': return 'DPGF'
      default: return type.toUpperCase()
    }
  }

  // Traduction des rôles en français
  const getRoleDisplayName = (role: string): string => {
    const roleNames: Record<string, string> = {
      'MOA': 'Maître d\'ouvrage',
      'MOADEL': 'Maître d\'ouvrage délégué',
      'ARCHI': 'Architecte',
      'BE': 'Bureau d\'études',
      'BC': 'Bureau de contrôle',
      'OPC': 'Coordinateur OPC',
      'ENT': 'Entreprise',
      'FO': 'Fournisseur'
    }
    return roleNames[role] || role
  }

  // Générer la liste des entreprises (colonne gauche) - Style HARMONIA
  const companiesHTML = otherCompanies.map(company => {
    console.log('🏢 Company data:', company)
    return `
      <div class="entreprise-block">
        <div class="entreprise-header">
          <div class="logo-container">
            ${company.logo_url ? 
              `<img src="${baseUrl}${company.logo_url}" alt="Logo ${company.name}" class="company-logo" />` : 
              `<div class="logo-placeholder">${company.name.substring(0,3).toUpperCase()}</div>`
            }
          </div>
          <div class="company-info">
            <div class="role-label">${getRoleDisplayName(company.role).toUpperCase()} :</div>
            <div class="company-name">${company.name}</div>
            ${company.activity ? `<div class="company-activity">${company.activity}</div>` : ''}
            ${company.address ? `<div class="company-address">${company.address}</div>` : ''}
            ${company.phone ? `<div class="company-contact">Tél : ${company.phone}</div>` : ''}
            ${company.email ? `<div class="company-contact">Mail : ${company.email}</div>` : ''}
          </div>
        </div>
      </div>
    `
  }).join('')

  // Générer les infos du projet (colonne droite) - Style HARMONIA
  const projectHTML = `
    <!-- En-tête avec logos partenaires -->
    <div class="header-logos">
      <div class="logo-group">
        <img src="/api/placeholder/80/40" alt="Logo 1" class="header-logo" />
        <img src="/api/placeholder/80/40" alt="Logo 2" class="header-logo" />
        <img src="/api/placeholder/80/40" alt="Logo 3" class="header-logo" />
      </div>
    </div>

    <!-- Titre du projet -->
    <div class="project-title-section">
      <h1 class="project-main-title">${data.lot.project?.name || 'Projet non défini'}</h1>
      ${data.lot.project?.address ? `<div class="project-address">${data.lot.project.address}</div>` : ''}
    </div>

    <!-- Image du projet -->
    ${data.lot.photo_url || data.lot.project?.photo_url ? `
      <div class="project-image-section">
        <img src="${baseUrl}${data.lot.photo_url || data.lot.project?.photo_url}"
             class="project-image"
             alt="Image du projet" />
      </div>
    ` : ''}

    <!-- Maître d'ouvrage -->
    ${moaCompany ? `
      <div class="moa-section">
        <div class="moa-header">MAÎTRE D'OUVRAGE</div>
        <div class="moa-content">
          <div class="moa-logo">
            ${moaCompany.logo_url ? `<img src="${baseUrl}${moaCompany.logo_url}" alt="Logo MOA" />` : ''}
          </div>
          <div class="moa-info">
            <div class="moa-name">${moaCompany.name}</div>
            ${moaCompany.address ? `<div class="moa-address">${moaCompany.address}</div>` : ''}
            ${moaCompany.phone ? `<div class="moa-contact">Tél : ${moaCompany.phone}</div>` : ''}
          </div>
        </div>
      </div>
    ` : ''}

    <!-- Lot -->
    <div class="lot-section">
      <div class="lot-title">Lot n°${data.lot.code || data.lot.id}</div>
      <div class="lot-description">${data.lot.description || data.lot.name}</div>
    </div>

    <!-- Type de document -->
    <div class="document-section">
      <div class="document-title">${getDocumentTitle(data.documentType)}</div>
    </div>

    <!-- Tableau d'informations -->
    <div class="info-section">
      <table class="info-table">
        <tr>
          <td class="info-label">Dossier</td>
          <td class="info-value">001</td>
        </tr>
        <tr>
          <td class="info-label">Date</td>
          <td class="info-value">${new Date().toLocaleDateString('fr-FR')}</td>
        </tr>
        <tr>
          <td class="info-label">Phase</td>
          <td class="info-value">${data.lot.current_phase || 'PRO'}</td>
        </tr>
        <tr>
          <td class="info-label">Indice</td>
          <td class="info-value">${data.documentIndice}</td>
        </tr>
      </table>
    </div>
  `

  // HTML final - Style HARMONIA
  const html = `
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: Arial, sans-serif;
      background-color: #f0f0f0;
      padding: 20px;
    }

    .cover-page {
      width: 210mm;
      height: 297mm;
      margin: 0 auto;
      background-color: white;
      border: 2px solid #333;
      display: flex;
      overflow: hidden;
      position: relative;
    }

    /* Colonne gauche - Entreprises */
    .left-column {
      width: 40%;
      background-color: #e8e8e8;
      padding: 15px;
      display: flex;
      flex-direction: column;
      gap: 15px;
    }

    .entreprise-block {
      background-color: white;
      border: 1px solid #ccc;
      padding: 8px;
      border-radius: 4px;
    }

    .entreprise-header {
      display: flex;
      align-items: flex-start;
      gap: 8px;
    }

    .logo-container {
      width: 50px;
      height: 50px;
      flex-shrink: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 1px solid #ddd;
      background-color: white;
    }

    .company-logo {
      max-width: 48px;
      max-height: 48px;
      object-fit: contain;
    }

    .logo-placeholder {
      width: 100%;
      height: 100%;
      background-color: #f0f0f0;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 10px;
      color: #666;
      font-weight: bold;
    }

    .company-info {
      flex: 1;
      font-size: 9px;
      line-height: 1.3;
    }

    .role-label {
      font-weight: bold;
      color: #333;
      margin-bottom: 2px;
      font-size: 8px;
    }

    .company-name {
      font-weight: bold;
      color: #000;
      margin-bottom: 2px;
      font-size: 9px;
    }

    .company-activity {
      color: #666;
      margin-bottom: 1px;
      font-size: 8px;
    }

    .company-address {
      color: #666;
      margin-bottom: 1px;
      font-size: 8px;
    }

    .company-contact {
      color: #666;
      font-size: 8px;
    }

    /* Colonne droite - Projet */
    .right-column {
      width: 60%;
      background-color: white;
      padding: 15px;
      display: flex;
      flex-direction: column;
      position: relative;
    }

    .header-logos {
      position: absolute;
      top: 10px;
      right: 15px;
      display: flex;
      gap: 5px;
    }

    .logo-group {
      display: flex;
      gap: 5px;
    }

    .header-logo {
      height: 30px;
      width: auto;
      object-fit: contain;
    }

    .project-title-section {
      text-align: center;
      margin-top: 60px;
      margin-bottom: 20px;
    }

    .project-main-title {
      font-size: 14px;
      font-weight: bold;
      color: #333;
      margin-bottom: 8px;
    }

    .project-address {
      font-size: 12px;
      color: #666;
      font-weight: 500;
    }

    .project-image-section {
      text-align: center;
      margin-bottom: 20px;
    }

    .project-image {
      max-width: 100%;
      max-height: 120px;
      object-fit: cover;
      border: 1px solid #ccc;
    }

    .moa-section {
      border: 2px solid #333;
      margin-bottom: 15px;
      background-color: #f9f9f9;
    }

    .moa-header {
      background-color: #333;
      color: white;
      text-align: center;
      padding: 5px;
      font-size: 11px;
      font-weight: bold;
    }

    .moa-content {
      padding: 8px;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .moa-logo img {
      height: 40px;
      width: auto;
      object-fit: contain;
    }

    .moa-name {
      font-weight: bold;
      font-size: 11px;
      margin-bottom: 2px;
    }

    .moa-address, .moa-contact {
      font-size: 9px;
      color: #666;
    }

    .lot-section {
      text-align: center;
      margin-bottom: 15px;
    }

    .lot-title {
      font-size: 16px;
      font-weight: bold;
      color: #333;
      margin-bottom: 5px;
    }

    .lot-description {
      font-size: 12px;
      font-weight: bold;
      text-transform: uppercase;
      color: #666;
      letter-spacing: 0.5px;
    }

    .document-section {
      text-align: center;
      margin-bottom: 20px;
    }

    .document-title {
      background-color: #e8e8e8;
      border: 2px solid #333;
      border-radius: 10px;
      padding: 8px;
      font-size: 12px;
      font-weight: bold;
      color: #333;
    }

    .info-section {
      margin-top: auto;
      margin-bottom: 20px;
    }

    .info-table {
      width: 100%;
      border-collapse: collapse;
      border: 2px solid #333;
      font-size: 10px;
    }

    .info-label {
      background-color: #e8e8e8;
      border: 1px solid #333;
      padding: 4px 8px;
      font-weight: bold;
      width: 40%;
    }

    .info-value {
      border: 1px solid #333;
      padding: 4px 8px;
      text-align: center;
      font-weight: bold;
    }
  </style>
</head>
<body>
  <div class="cover-page">
    <div class="left-column">
      ${companiesHTML}
    </div>
    <div class="right-column">
      ${projectHTML}
    </div>
  </div>
</body>
</html>`

  console.log('✅ Page de garde HARMONIA générée avec succès')
  return html
}

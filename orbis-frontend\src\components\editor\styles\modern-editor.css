/* Modern Document Editor Styles - Google Docs like */

.modern-editor-container {
  display: flex;
  height: 100vh;
  background: #f8f9fa;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Sidebar */
.modern-sidebar {
  background: white;
  border-right: 1px solid #e5e7eb;
  transition: width 0.3s ease;
  display: flex;
  flex-direction: column;
  box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
  z-index: 20;
}

.modern-sidebar.closed {
  width: 60px;
}

.modern-sidebar.open {
  width: 280px;
}

.sidebar-header {
  padding: 16px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  gap: 12px;
}

.burger-btn {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  color: #374151;
  transition: background-color 0.2s;
}

.burger-btn:hover {
  background: #f3f4f6;
}

.sidebar-title {
  font-weight: 600;
  color: #111827;
  font-size: 16px;
}

.sidebar-content {
  padding: 16px;
  overflow-y: auto;
  flex: 1;
}

.tool-section {
  margin-bottom: 24px;
}

.tool-section h3 {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.tool-btn {
  display: block;
  width: 100%;
  padding: 12px 16px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  cursor: pointer;
  margin-bottom: 8px;
  text-align: left;
  font-size: 14px;
  color: #374151;
  transition: all 0.2s;
}

.tool-btn:hover {
  background: #f9fafb;
  border-color: #0F766E;
  color: #0F766E;
}

.tool-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

.tool-btn-small {
  padding: 8px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  color: #374151;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tool-btn-small:hover {
  background: #f9fafb;
  border-color: #0F766E;
}

.tool-btn-small.active {
  background: #0F766E;
  color: white;
  border-color: #0F766E;
}

/* Main Editor Area */
.editor-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: white;
  margin: 0 auto;
  max-width: 1200px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

/* Top Toolbar */
.top-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 24px;
  border-bottom: 1px solid #e5e7eb;
  background: white;
  position: sticky;
  top: 0;
  z-index: 10;
}

.toolbar-left,
.toolbar-center,
.toolbar-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.heading-select {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  font-size: 14px;
  color: #374151;
  cursor: pointer;
}

.document-title {
  font-weight: 600;
  color: #111827;
  font-size: 16px;
}

.toolbar-btn {
  padding: 8px 12px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  color: #374151;
  transition: all 0.2s;
}

.toolbar-btn:hover:not(:disabled) {
  background: #f9fafb;
  border-color: #0F766E;
  color: #0F766E;
}

.toolbar-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Editor Content */
.editor-content {
  flex: 1;
  overflow-y: auto;
  padding: 40px 80px;
  background: white;
  line-height: 1.6;
}

.editor-content .ProseMirror {
  outline: none;
  min-height: calc(100vh - 200px);
  font-size: 16px;
  line-height: 1.6;
  color: #111827;
}

.editor-content .ProseMirror:focus {
  outline: none;
}

/* CCTP Header Styles */
.cctp-header {
  text-align: center;
  margin-bottom: 40px;
  padding: 20px;
  border-bottom: 2px solid #0F766E;
}

.workspace-logo {
  max-height: 80px;
  margin-bottom: 20px;
}

/* Status Bar */
.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 24px;
  background: #f8f9fa;
  border-top: 1px solid #e5e7eb;
  font-size: 12px;
  color: #6b7280;
}

.status-left,
.status-right {
  display: flex;
  gap: 16px;
}

.readonly-indicator {
  color: #dc2626;
  font-weight: 500;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 50;
}

.modal-content {
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  max-width: 500px;
  width: 90%;
}

.modal-content h3 {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: #111827;
}

.modal-content p {
  margin: 0 0 20px 0;
  color: #6b7280;
}

.file-input {
  width: 100%;
  padding: 8px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  margin-bottom: 20px;
}

.modal-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.btn-cancel {
  padding: 8px 16px;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  cursor: pointer;
  color: #374151;
}

.btn-primary {
  padding: 8px 16px;
  background: #0F766E;
  border: 1px solid #0F766E;
  border-radius: 4px;
  cursor: pointer;
  color: white;
}

.btn-cancel:hover {
  background: #f9fafb;
}

.btn-primary:hover {
  background: #0d5b56;
}

/* Responsive Design */
@media (max-width: 768px) {
  .modern-sidebar.open {
    width: 240px;
  }
  
  .editor-content {
    padding: 20px 16px;
  }
  
  .top-toolbar {
    padding: 8px 16px;
  }
}

/* Scrollbar Styling */
.editor-content::-webkit-scrollbar {
  width: 8px;
}

.editor-content::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.editor-content::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

.editor-content::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Print Styles - Correspondance exacte avec l'affichage écran */
@media print {
  /* Masquer les éléments d'interface */
  .modern-sidebar,
  .simple-sidebar,
  .tools-sidebar,
  .google-toolbar,
  .top-toolbar,
  .status-bar,
  .sidebar-actions,
  .toolbar-row-1,
  .toolbar-row-2,
  .toolbar-single-row {
    display: none !important;
  }

  /* Supprimer les marges et paddings de la page */
  body, html {
    margin: 0 !important;
    padding: 0 !important;
  }

  .google-docs-editor,
  .editor-main-area,
  .editor-main {
    background: white !important;
    box-shadow: none !important;
    max-width: none !important;
    height: auto !important;
    overflow: visible !important;
  }

  .editor-content {
    padding: 0 !important;
    background: white !important;
    display: block !important;
    overflow: visible !important;
  }

  .editor-content .ProseMirror {
    /* Dimensions A4 exactes pour l'impression */
    width: 210mm !important;
    min-height: 297mm !important;
    padding: 30px !important;
    margin: 0 !important;
    box-shadow: none !important;

    /* Assurer la correspondance typographique */
    font-family: 'Times New Roman', serif !important;
    font-size: 12pt !important;
    line-height: 1.5 !important;
    color: #000 !important;

    /* Éviter les coupures de page au milieu des éléments */
    page-break-inside: avoid;
  }

  /* Gestion des sauts de page */
  .editor-content .ProseMirror h1,
  .editor-content .ProseMirror h2,
  .editor-content .ProseMirror h3 {
    page-break-after: avoid;
    page-break-inside: avoid;
  }

  .editor-content .ProseMirror table {
    page-break-inside: avoid;
  }

  /* Configuration de la page d'impression */
  @page {
    size: A4;
    margin: 0; /* Pas de marges supplémentaires, on gère tout dans le ProseMirror */
  }
}

/* Styles pour les sauts de page */
.page-break {
  height: 1px;
  background: transparent;
  border: none;
  page-break-before: always;
  margin: 20px 0;
  padding: 0;
  position: relative;
  border-top: 1px dashed #ccc;
}

.page-break::before {
  content: "--- Saut de page ---";
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  background: white;
  padding: 2px 8px;
  font-size: 10px;
  color: #666;
  border: 1px dashed #ccc;
  border-radius: 3px;
  white-space: nowrap;
}

/* Styles pour les boutons de sauvegarde et fermeture */
.tool-btn.unsaved {
  background: #fef3c7;
  border-color: #f59e0b;
  color: #92400e;
}

.tool-btn.unsaved:hover {
  background: #fde68a;
}

.tool-btn.close-btn {
  background: #fee2e2;
  border-color: #f87171;
  color: #dc2626;
}

.tool-btn.close-btn:hover {
  background: #fecaca;
}

.btn-secondary {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.btn-secondary:hover {
  background: #e5e7eb;
}

/* Nouveaux styles pour les outils étendus */
.tool-select {
  width: 100%;
  padding: 8px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  font-size: 14px;
  margin-bottom: 8px;
}

.tool-row {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
}

.color-picker {
  width: 32px;
  height: 32px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  cursor: pointer;
  background: none;
  padding: 0;
}

.tool-btn-small:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.tool-btn-small:disabled:hover {
  background: #f9fafb;
}

.tool-btn.active {
  background: #dbeafe;
  border-color: #3b82f6;
  color: #1d4ed8;
}

/* Styles Google Docs */
.google-docs-editor {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f8f9fa;
}

.google-toolbar {
  background: white;
  border-bottom: 1px solid #e0e0e0;
  padding: 12px 24px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.toolbar-row-1, .toolbar-row-2, .toolbar-single-row {
  display: flex;
  align-items: center;
  gap: 20px;
  margin: 6px 0;
}

.toolbar-section {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 0 12px;
  border-right: 1px solid #e0e0e0;
}

.toolbar-section:last-child {
  border-right: none;
}

.toolbar-btn {
  background: none;
  border: none;
  padding: 10px 14px;
  border-radius: 6px;
  color: #5f6368;
  cursor: pointer;
  font-size: 24px;
  font-weight: 500;
  transition: all 0.2s;
  min-width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.toolbar-btn:hover {
  background: #f1f3f4;
  color: #202124;
}

.toolbar-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.toolbar-btn.active {
  background: #e8f0fe;
  color: #1a73e8;
}

.toolbar-btn.unsaved {
  background: #fef7e0;
  color: #ea8600;
}

.toolbar-btn.close-btn {
  background: #fce8e6;
  color: #d93025;
}

.toolbar-btn.article-btn {
  background: #e6f4ea;
  color: #137333;
  font-weight: 500;
}

.font-selector, .size-selector {
  border: 1px solid #dadce0;
  border-radius: 6px;
  padding: 10px 14px;
  background: white;
  color: #3c4043;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  height: 48px;
}

.font-selector {
  min-width: 160px;
}

.size-selector {
  min-width: 90px;
}

.color-btn {
  width: 40px;
  height: 40px;
  border: 1px solid #dadce0;
  border-radius: 6px;
  cursor: pointer;
  background: none;
}

.editor-main-area {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* Sidebar simple à gauche */
.simple-sidebar {
  width: 300px;
  background: white;
  border-right: 1px solid #e0e0e0;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.lot-info {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #0F766E;
}

.lot-name {
  font-size: 16px;
  font-weight: 600;
  color: #333333;
  margin-bottom: 4px;
}

.lot-subtitle {
  font-size: 14px;
  color: #666;
}

/* Bloc du haut : Actions de création */
.sidebar-top-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* Bloc du bas : Actions de sauvegarde */
.sidebar-bottom-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: auto; /* Pousse les boutons vers le bas */
}

/* Ancienne classe pour compatibilité */
.sidebar-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: auto;
}

.sidebar-btn {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #dadce0;
  border-radius: 6px;
  background: white;
  color: #3c4043;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  position: relative;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.sidebar-btn:hover {
  background: #f8f9fa;
  border-color: #bdc1c6;
}

.sidebar-btn.save-btn {
  background: #0F766E;
  color: white;
  border-color: #0F766E;
}

.sidebar-btn.save-btn:hover {
  background: #0d6660;
}

.sidebar-btn.save-btn.unsaved {
  background: #ea8600;
  border-color: #ea8600;
}

.sidebar-btn.close-btn {
  background: #d93025;
  color: white;
  border-color: #d93025;
}

.sidebar-btn.close-btn:hover {
  background: #c5221a;
}

/* États de chargement pour les boutons */
.sidebar-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  pointer-events: none;
}

.sidebar-btn.loading {
  background: #f0f0f0;
  color: #666;
  border-color: #ccc;
}

/* Animation du spinner */
.spinner {
  display: inline-block;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Styles spécifiques pour les boutons de génération */
.sidebar-btn.generate-cover-btn.loading {
  background: #e8f5f3;
  color: #0F766E;
  border-color: #0F766E;
}

.sidebar-btn.add-article-btn.loading {
  background: #e8f5f3;
  color: #0F766E;
  border-color: #0F766E;
}

.tools-sidebar {
  background: white;
  border-right: 1px solid #e0e0e0;
  transition: width 0.3s ease;
  overflow-y: auto;
}

.tools-sidebar.open {
  width: 280px;
}

.tools-sidebar.closed {
  width: 48px;
}

.sidebar-header {
  padding: 12px;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.sidebar-toggle {
  background: none;
  border: none;
  padding: 8px;
  border-radius: 4px;
  color: #5f6368;
  cursor: pointer;
  font-size: 16px;
}

.sidebar-toggle:hover {
  background: #f1f3f4;
}

.sidebar-title {
  font-weight: 500;
  color: #3c4043;
}

.sidebar-content {
  padding: 16px;
}

.tool-section {
  margin-bottom: 24px;
}

.tool-section h3 {
  font-size: 14px;
  font-weight: 500;
  color: #3c4043;
  margin: 0 0 12px 0;
}

.tool-btn {
  width: 100%;
  background: none;
  border: 1px solid #dadce0;
  padding: 8px 12px;
  border-radius: 4px;
  color: #3c4043;
  cursor: pointer;
  font-size: 14px;
  margin-bottom: 8px;
  text-align: left;
  transition: all 0.2s;
}

.tool-btn:hover {
  background: #f8f9fa;
  border-color: #bdc1c6;
}

.tool-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 4px;
  margin-bottom: 8px;
}

.tool-btn-small {
  background: none;
  border: 1px solid #dadce0;
  padding: 6px;
  border-radius: 4px;
  color: #3c4043;
  cursor: pointer;
  font-size: 12px;
  text-align: center;
  transition: all 0.2s;
}

.tool-btn-small:hover {
  background: #f8f9fa;
  border-color: #bdc1c6;
}

.tool-btn-small.active {
  background: #e8f0fe;
  border-color: #1a73e8;
  color: #1a73e8;
}

.tool-select {
  width: 100%;
  padding: 8px;
  border: 1px solid #dadce0;
  border-radius: 4px;
  background: white;
  color: #3c4043;
  font-size: 14px;
  margin-bottom: 8px;
}

.tool-row {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
}

.color-picker {
  width: 32px;
  height: 32px;
  border: 1px solid #dadce0;
  border-radius: 4px;
  cursor: pointer;
  background: none;
  padding: 0;
}

/* Styles pour l'éditeur principal */
.editor-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #f8f9fa;
  overflow: hidden;
  max-width: none; /* Supprime la limitation de largeur */
}

.editor-content {
  flex: 1;
  padding: 40px;
  overflow-y: auto;
  background: #f8f9fa;
  display: flex;
  justify-content: center; /* Centre le document */
}

.editor-content .ProseMirror {
  /* Dimensions A4 adaptées pour l'écran (ratio 1:1.414) */
  width: 794px;  /* 210mm à 96 DPI = 794px */
  min-height: 1123px; /* 297mm à 96 DPI = 1123px */
  background: white;
  box-shadow: 0 0 10px rgba(0,0,0,0.1);
  outline: none;

  /* Marges ajustées : 30px */
  padding: 30px;

  /* Typographie pour correspondre aux documents imprimés */
  font-family: 'Times New Roman', serif;
  font-size: 12pt;
  line-height: 1.5;
  color: #000;

  /* Assure que le contenu ne déborde pas */
  box-sizing: border-box;
}



.editor-content .ProseMirror h1,
.editor-content .ProseMirror h2,
.editor-content .ProseMirror h3,
.editor-content .ProseMirror h4,
.editor-content .ProseMirror h5,
.editor-content .ProseMirror h6 {
  font-weight: bold;
  margin-top: 1em;
  margin-bottom: 0.5em;
}

.editor-content .ProseMirror h1 { font-size: 18pt; }
.editor-content .ProseMirror h2 { font-size: 16pt; }
.editor-content .ProseMirror h3 { font-size: 14pt; }
.editor-content .ProseMirror h4 { font-size: 12pt; }
.editor-content .ProseMirror h5 { font-size: 11pt; }
.editor-content .ProseMirror h6 { font-size: 10pt; }

.editor-content .ProseMirror p {
  margin-bottom: 1em;
}

.editor-content .ProseMirror ul,
.editor-content .ProseMirror ol {
  padding-left: 1.5em;
  margin-bottom: 1em;
}

.editor-content .ProseMirror table {
  border-collapse: collapse;
  width: 100%;
  margin: 1em 0;
}

.editor-content .ProseMirror table td,
.editor-content .ProseMirror table th {
  border: 1px solid #ccc;
  padding: 8px;
  text-align: left;
}

.editor-content .ProseMirror table th {
  background-color: #f5f5f5;
  font-weight: bold;
}

/* Responsive pour petits écrans */
@media (max-width: 1024px) {
  .editor-content {
    padding: 20px;
  }

  .editor-content .ProseMirror {
    /* Sur petits écrans, adapter la largeur mais garder les proportions A4 */
    width: calc(100vw - 40px);
    max-width: 210mm;
    padding: 30px;
    min-height: auto;
  }

  .tools-sidebar.open {
    width: 240px;
  }

  .toolbar-row-2 {
    flex-wrap: wrap;
  }

  .toolbar-section {
    margin-bottom: 4px;
  }
}

/* Pour les très petits écrans (mobiles) */
@media (max-width: 768px) {
  .simple-sidebar {
    width: 250px;
  }

  .editor-content {
    padding: 10px;
  }

  .editor-content .ProseMirror {
    width: calc(100vw - 20px);
    padding: 30px;
    font-size: 11pt;
  }
}

'use client'

import React from 'react'
import { Editor } from '@tiptap/react'
import { OrbisDocConfig } from '../types'

interface EditorToolbarProps {
  editor: Editor
  config: OrbisDocConfig
  isSaving: boolean
  hasUnsavedChanges: boolean
  onSave: () => Promise<void>
  onInsertPageBreak: () => void
  onInsertCoverPage: () => void
  onConfigureFooter?: () => void
}

export default function EditorToolbar({
  editor,
  config,
  isSaving,
  hasUnsavedChanges,
  onSave,
  onInsertPageBreak,
  onInsertCoverPage,
  onConfigureFooter
}: EditorToolbarProps) {

  const handleSave = async () => {
    try {
      await onSave()
    } catch (error) {
      console.error('Erreur lors de la sauvegarde:', error)
    }
  }

  return (
    <div className="orbis-doc-toolbar bg-white border-b border-gray-200 p-4">
      <div className="flex items-center justify-between">
        {/* Groupe de gauche - Actions principales */}
        <div className="flex items-center space-x-4">
          {/* Sauvegarde */}
          <button
            onClick={handleSave}
            disabled={isSaving || !hasUnsavedChanges}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              hasUnsavedChanges
                ? 'bg-blue-600 text-white hover:bg-blue-700'
                : 'bg-gray-100 text-gray-400 cursor-not-allowed'
            }`}
          >
            {isSaving ? 'Sauvegarde...' : 'Sauvegarder'}
          </button>

          {/* Indicateur de modifications */}
          {hasUnsavedChanges && (
            <span className="text-sm text-orange-600">
              • Modifications non sauvegardées
            </span>
          )}
        </div>

        {/* Groupe du centre - Outils de formatage */}
        <div className="flex items-center space-x-2">
          {/* Formatage de base */}
          <div className="flex items-center space-x-1 border-r border-gray-300 pr-2">
            <button
              onClick={() => editor.chain().focus().toggleBold().run()}
              className={`p-2 rounded hover:bg-gray-100 ${
                editor.isActive('bold') ? 'bg-gray-200' : ''
              }`}
              title="Gras"
            >
              <strong>B</strong>
            </button>
            <button
              onClick={() => editor.chain().focus().toggleItalic().run()}
              className={`p-2 rounded hover:bg-gray-100 ${
                editor.isActive('italic') ? 'bg-gray-200' : ''
              }`}
              title="Italique"
            >
              <em>I</em>
            </button>
            <button
              onClick={() => editor.chain().focus().toggleUnderline().run()}
              className={`p-2 rounded hover:bg-gray-100 ${
                editor.isActive('underline') ? 'bg-gray-200' : ''
              }`}
              title="Souligné"
            >
              <u>U</u>
            </button>
          </div>

          {/* Titres */}
          <div className="flex items-center space-x-1 border-r border-gray-300 pr-2">
            {[1, 2, 3].map((level) => (
              <button
                key={level}
                onClick={() => editor.chain().focus().toggleNumberedHeading({ level }).run()}
                className={`px-2 py-1 rounded text-sm hover:bg-gray-100 ${
                  editor.isActive('numberedHeading', { level }) ? 'bg-gray-200' : ''
                }`}
                title={`Titre ${level}`}
              >
                H{level}
              </button>
            ))}
          </div>

          {/* Listes */}
          <div className="flex items-center space-x-1 border-r border-gray-300 pr-2">
            <button
              onClick={() => editor.chain().focus().toggleBulletList().run()}
              className={`p-2 rounded hover:bg-gray-100 ${
                editor.isActive('bulletList') ? 'bg-gray-200' : ''
              }`}
              title="Liste à puces"
            >
              •
            </button>
            <button
              onClick={() => editor.chain().focus().toggleOrderedList().run()}
              className={`p-2 rounded hover:bg-gray-100 ${
                editor.isActive('orderedList') ? 'bg-gray-200' : ''
              }`}
              title="Liste numérotée"
            >
              1.
            </button>
          </div>

          {/* Alignement */}
          <div className="flex items-center space-x-1 border-r border-gray-300 pr-2">
            {['left', 'center', 'right', 'justify'].map((align) => (
              <button
                key={align}
                onClick={() => editor.chain().focus().setTextAlign(align).run()}
                className={`p-2 rounded hover:bg-gray-100 ${
                  editor.isActive({ textAlign: align }) ? 'bg-gray-200' : ''
                }`}
                title={`Aligner à ${align === 'left' ? 'gauche' : align === 'center' ? 'centre' : align === 'right' ? 'droite' : 'justifier'}`}
              >
                {align === 'left' && '⬅'}
                {align === 'center' && '⬌'}
                {align === 'right' && '➡'}
                {align === 'justify' && '⬍'}
              </button>
            ))}
          </div>
        </div>

        {/* Groupe de droite - Actions spéciales */}
        <div className="flex items-center space-x-2">
          <button
            onClick={onInsertCoverPage}
            className="px-3 py-2 bg-green-600 text-white rounded-md text-sm hover:bg-green-700"
            title="Insérer une page de garde"
          >
            Page de garde
          </button>

          <button
            onClick={onInsertPageBreak}
            className="px-3 py-2 bg-gray-600 text-white rounded-md text-sm hover:bg-gray-700"
            title="Insérer un saut de page"
          >
            Saut de page
          </button>

          {onConfigureFooter && (
            <button
              onClick={onConfigureFooter}
              className="px-3 py-2 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700"
              title="Configurer le pied de page"
            >
              Pied de page
            </button>
          )}
        </div>
      </div>
    </div>
  )
}

'use client'

import React, { useState } from 'react'
import { Editor } from '@tiptap/react'

interface HeadingToolsProps {
  editor: Editor
}

export default function HeadingTools({ editor }: HeadingToolsProps) {
  const [showHeadingMenu, setShowHeadingMenu] = useState(false)

  const headingLevels = [
    { level: 1, label: 'Titre 1', size: 'text-2xl' },
    { level: 2, label: 'Titre 2', size: 'text-xl' },
    { level: 3, label: 'Titre 3', size: 'text-lg' },
    { level: 4, label: 'Titre 4', size: 'text-base' },
    { level: 5, label: 'Titre 5', size: 'text-sm' },
    { level: 6, label: 'Titre 6', size: 'text-xs' }
  ]

  const getCurrentHeadingLevel = () => {
    for (let level = 1; level <= 6; level++) {
      if (editor.isActive('numberedHeading', { level })) {
        return level
      }
    }
    return null
  }

  const currentLevel = getCurrentHeadingLevel()

  return (
    <div className="flex items-center space-x-1 border-r border-gray-300 pr-3 mr-3 relative">
      {/* Sélecteur de niveau de titre */}
      <div className="relative">
        <button
          onClick={() => setShowHeadingMenu(!showHeadingMenu)}
          className={`px-3 py-2 rounded hover:bg-gray-100 transition-colors flex items-center space-x-1 ${
            currentLevel ? 'bg-blue-100 text-blue-700' : 'text-gray-700'
          }`}
          title="Sélectionner un niveau de titre"
        >
          <span className="text-sm font-medium">
            {currentLevel ? `H${currentLevel}` : 'Titre'}
          </span>
          <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
          </svg>
        </button>

        {showHeadingMenu && (
          <div className="absolute top-full left-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-10 min-w-[200px]">
            <div className="py-1">
              <button
                onClick={() => {
                  editor.chain().focus().setParagraph().run()
                  setShowHeadingMenu(false)
                }}
                className="w-full px-4 py-2 text-left hover:bg-gray-100 text-sm"
              >
                Paragraphe normal
              </button>
              
              {headingLevels.map(({ level, label, size }) => (
                <button
                  key={level}
                  onClick={() => {
                    editor.chain().focus().toggleNumberedHeading({ level }).run()
                    setShowHeadingMenu(false)
                  }}
                  className={`w-full px-4 py-2 text-left hover:bg-gray-100 ${size} font-semibold ${
                    editor.isActive('numberedHeading', { level }) ? 'bg-blue-50 text-blue-700' : ''
                  }`}
                >
                  {label}
                </button>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Boutons de titre rapides */}
      <div className="flex items-center space-x-1">
        {[1, 2, 3].map((level) => (
          <button
            key={level}
            onClick={() => editor.chain().focus().toggleNumberedHeading({ level }).run()}
            className={`px-2 py-1 rounded text-sm hover:bg-gray-100 transition-colors ${
              editor.isActive('numberedHeading', { level }) ? 'bg-blue-100 text-blue-700' : 'text-gray-700'
            }`}
            title={`Titre ${level}`}
          >
            H{level}
          </button>
        ))}
      </div>

      {/* Renumérotation */}
      <div className="flex items-center space-x-1 border-l border-gray-200 pl-2 ml-2">
        <button
          onClick={() => editor.chain().focus().renumberHeadings().run()}
          className="px-2 py-1 rounded text-xs hover:bg-gray-100 transition-colors text-gray-700"
          title="Renuméroter tous les titres"
        >
          1.2.3
        </button>
      </div>

      {/* Fermer le menu si on clique ailleurs */}
      {showHeadingMenu && (
        <div
          className="fixed inset-0 z-0"
          onClick={() => setShowHeadingMenu(false)}
        />
      )}
    </div>
  )
}

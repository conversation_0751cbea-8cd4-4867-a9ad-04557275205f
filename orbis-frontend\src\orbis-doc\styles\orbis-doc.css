/* Styles principaux pour orbis-doc */
@import './cover-page-templates.css';

.orbis-doc-editor {
  @apply w-full h-full flex flex-col bg-gray-50;
}

.orbis-doc-loading {
  @apply flex items-center justify-center h-64;
}

.loading-spinner {
  @apply text-center;
}

.spinner {
  @apply animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4;
}

.orbis-doc-error {
  @apply flex items-center justify-center h-64 text-red-600;
}

.orbis-doc-main {
  @apply flex-1 flex flex-col overflow-auto;
  max-height: calc(100vh - 120px); /* Ajuster selon la hauteur de la toolbar */
}

/* Container des pages */
.document-pages-container {
  @apply flex-1 overflow-auto p-6;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* Styles des pages A4 */
.document-page {
  @apply bg-white mx-auto mb-8 relative;
  width: 210mm;
  min-height: 297mm;
  padding: 25mm 20mm 35mm 20mm; /* Padding bottom plus grand pour le footer */
  box-shadow:
    0 1px 3px rgba(0, 0, 0, 0.12),
    0 1px 2px rgba(0, 0, 0, 0.24),
    0 8px 16px rgba(0, 0, 0, 0.1);
  border-radius: 2px;
  transition: box-shadow 0.3s ease;
}

.document-page:hover {
  box-shadow:
    0 3px 6px rgba(0, 0, 0, 0.16),
    0 3px 6px rgba(0, 0, 0, 0.23),
    0 12px 24px rgba(0, 0, 0, 0.15);
}

/* Variantes de tailles de page */
.document-page.A3 {
  width: 297mm;
  min-height: 420mm;
}

.document-page.Letter {
  width: 216mm;
  min-height: 279mm;
}

.document-page.Legal {
  width: 216mm;
  min-height: 356mm;
}

/* Contenu de la page */
.page-content {
  @apply relative;
  min-height: calc(297mm - 60mm); /* Hauteur - marges top/bottom */
  font-family: 'Times New Roman', serif;
  font-size: 12pt;
  line-height: 1.5;
  color: #333;
}

/* Pied de page */
.page-footer {
  @apply absolute bottom-0 left-0 right-0 text-xs text-gray-600 border-t border-gray-200;
  margin: 0 20mm;
  padding: 8mm 0 10mm 0;
  height: 15mm;
}

.footer-content {
  @apply flex justify-between items-center h-full;
}

.footer-left,
.footer-center,
.footer-right {
  @apply flex-1;
}

.footer-center {
  @apply text-center;
}

.footer-right {
  @apply text-right;
}

/* Styles pour les templates de pieds de page */
.corporate-footer {
  @apply flex justify-between items-center;
}

.corporate-footer .footer-section {
  @apply flex items-center space-x-2;
}

.corporate-footer .footer-separator {
  @apply text-gray-400;
}

.corporate-footer .footer-title {
  @apply font-semibold;
}

.corporate-footer .footer-type {
  @apply text-gray-600;
}

.corporate-footer .footer-page {
  @apply font-medium;
}

.corporate-footer .footer-date {
  @apply text-gray-500;
}

.technical-footer {
  @apply flex justify-between items-start;
}

.technical-footer .footer-left {
  @apply flex flex-col;
}

.technical-footer .footer-right {
  @apply flex flex-col text-right;
}

.technical-footer .doc-ref {
  @apply text-xs font-semibold text-gray-800;
}

.technical-footer .doc-title {
  @apply text-xs text-gray-600;
}

.technical-footer .page-info {
  @apply text-xs font-medium;
}

.technical-footer .date-info {
  @apply text-xs text-gray-500;
}

/* Page de garde */
.cover-page {
  @apply flex flex-col justify-center items-center text-center h-full;
  padding: 40mm 20mm;
}

.cover-page-wrapper {
  @apply mb-8;
}

.cover-page-display {
  min-height: calc(297mm - 60mm);
}

.cover-page-title {
  @apply text-4xl font-bold mb-6 text-gray-900;
  font-family: 'Times New Roman', serif;
}

.cover-page-subtitle {
  @apply text-xl text-gray-600 mb-8;
  font-family: 'Times New Roman', serif;
}

.cover-page-meta {
  @apply space-y-3 text-gray-700;
  font-size: 14pt;
}

/* Page vierge */
.blank-page {
  @apply flex items-center justify-center text-gray-400;
  min-height: calc(297mm - 60mm);
}

.blank-page-text {
  @apply text-sm italic;
}

/* Saut de page */
.page-break-wrapper {
  @apply my-6;
}

.page-break-container {
  @apply relative;
}

.page-break-line {
  @apply border-t-2 border-dashed border-gray-300 relative;
  margin: 20px 0;
}

.page-break-preview {
  @apply mt-4 p-4 bg-gray-50 border border-gray-200 rounded text-center text-sm text-gray-600;
}

/* Toolbar */
.orbis-doc-toolbar {
  @apply sticky top-0 z-10;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Navigation des pages */
.page-navigator {
  @apply sticky top-16 z-10;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Contenu de l'éditeur */
.orbis-doc-content {
  @apply focus:outline-none;
}

.orbis-doc-content h1,
.orbis-doc-content h2,
.orbis-doc-content h3,
.orbis-doc-content h4,
.orbis-doc-content h5,
.orbis-doc-content h6 {
  @apply font-bold mb-4 mt-6;
  font-family: 'Times New Roman', serif;
}

.orbis-doc-content h1 { @apply text-2xl; }
.orbis-doc-content h2 { @apply text-xl; }
.orbis-doc-content h3 { @apply text-lg; }
.orbis-doc-content h4 { @apply text-base; }
.orbis-doc-content h5 { @apply text-sm; }
.orbis-doc-content h6 { @apply text-xs; }

.orbis-doc-content p {
  @apply mb-4;
  text-align: justify;
}

.orbis-doc-content ul,
.orbis-doc-content ol {
  @apply mb-4 pl-6;
}

.orbis-doc-content li {
  @apply mb-2;
}

.orbis-doc-content table {
  @apply w-full border-collapse border border-gray-300 mb-4;
}

.orbis-doc-content th,
.orbis-doc-content td {
  @apply border border-gray-300 px-3 py-2;
}

.orbis-doc-content th {
  @apply bg-gray-100 font-semibold;
}

/* Styles d'impression */
@media print {
  .orbis-doc-editor {
    @apply bg-white;
  }

  .orbis-doc-toolbar,
  .page-navigator {
    @apply hidden;
  }

  .document-pages-container {
    @apply p-0;
    background: white;
  }

  .document-page {
    @apply shadow-none m-0;
    width: 100%;
    min-height: 100vh;
    break-after: page;
    border-radius: 0;
  }

  .page-footer {
    position: fixed;
    bottom: 10mm;
  }

  .page-break-wrapper {
    break-after: page;
  }

  .page-break-line,
  .page-break-preview {
    @apply hidden;
  }
}

/* Responsive */
@media (max-width: 1024px) {
  .document-page {
    width: 95%;
    min-height: auto;
    padding: 20px;
    margin: 0 auto 20px;
  }

  .orbis-doc-toolbar {
    @apply px-2;
  }

  .orbis-doc-toolbar .flex {
    @apply flex-wrap gap-2;
  }
}

@media (max-width: 768px) {
  .document-page {
    width: 100%;
    padding: 15px;
    margin: 0 0 15px;
    box-shadow: none;
    border: 1px solid #e5e7eb;
  }

  .page-content {
    font-size: 11pt;
  }

  .cover-page-title {
    @apply text-2xl;
  }

  .cover-page-subtitle {
    @apply text-lg;
  }
}

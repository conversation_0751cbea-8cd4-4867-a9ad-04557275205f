import { useEffect, useRef, useState } from 'react'
import { Editor } from '@tiptap/react'

interface UsePaginationOptions {
  editor: Editor | null
  documentTitle?: string
  pageHeight?: number // en pixels
  footerHeight?: number // en pixels
}

export function usePagination({
  editor,
  documentTitle = 'Document',
  pageHeight = 1123, // A4 height at 96 DPI
  footerHeight = 60
}: UsePaginationOptions) {
  const [pages, setPages] = useState<number[]>([1])
  const [currentPage, setCurrentPage] = useState(1)
  const containerRef = useRef<HTMLDivElement>(null)
  const updateTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Fonction pour calculer la pagination
  const updatePagination = () => {
    if (!editor || !containerRef.current) return

    const container = containerRef.current
    const proseMirrorElement = container.querySelector('.ProseMirror')

    if (!proseMirrorElement) return

    // Vérifier s'il y a une page de garde
    const hasCoverPage = proseMirrorElement.querySelector('[data-type="cover-page"]')

    // Hauteur utilisable par page (sans le pied de page)
    const usablePageHeight = pageHeight - footerHeight - 60 // 60px pour les marges

    // Calculer la hauteur du contenu principal (sans la page de garde)
    let contentHeight = proseMirrorElement.scrollHeight

    if (hasCoverPage) {
      const coverPageElement = hasCoverPage as HTMLElement
      const coverPageHeight = coverPageElement.offsetHeight
      contentHeight = Math.max(0, contentHeight - coverPageHeight)
    }

    // Calculer le nombre de pages nécessaires pour le contenu principal
    // Page 1 = page vierge après la page de garde, puis le contenu
    const numberOfContentPages = Math.max(1, Math.ceil(contentHeight / usablePageHeight))

    // Si on a une page de garde, on ajoute une page vierge (page 1) puis le contenu
    const totalPages = hasCoverPage ? numberOfContentPages + 1 : numberOfContentPages

    // Mettre à jour la liste des pages (commence à 1 pour le contenu principal)
    const newPages = Array.from({ length: totalPages }, (_, i) => i + 1)
    setPages(newPages)

    // Ajouter les séparateurs de page dans le DOM
    addPageSeparators(totalPages, usablePageHeight, hasCoverPage)
  }

  // Fonction pour ajouter les séparateurs visuels de page
  const addPageSeparators = (numberOfPages: number, usablePageHeight: number, hasCoverPage?: Element | null) => {
    if (!containerRef.current) return

    const container = containerRef.current

    // Supprimer les anciens séparateurs
    const oldSeparators = container.querySelectorAll('.page-separator')
    oldSeparators.forEach(separator => separator.remove())

    // Si on a une page de garde, on commence les séparateurs après elle
    let startOffset = 0
    if (hasCoverPage) {
      const coverPageElement = hasCoverPage as HTMLElement
      startOffset = coverPageElement.offsetHeight + pageHeight // Page de garde + page vierge
    }

    // Ajouter les nouveaux séparateurs pour le contenu principal
    for (let i = 1; i < numberOfPages; i++) {
      const separator = document.createElement('div')
      separator.className = 'page-separator'
      separator.style.position = 'absolute'
      separator.style.top = `${startOffset + (i * pageHeight)}px`
      separator.style.left = '0'
      separator.style.right = '0'
      separator.style.height = '1px'
      separator.style.backgroundColor = '#e5e7eb'
      separator.style.zIndex = '10'

      container.appendChild(separator)
    }
  }

  // Fonction pour générer le pied de page
  const generateFooter = (pageNumber: number, isCoverPage: boolean = false, isBlankPage: boolean = false) => {
    // Pas de pied de page pour la page de garde
    if (isCoverPage) {
      return ''
    }

    // Pas de pied de page pour la page vierge
    if (isBlankPage) {
      return ''
    }

    return `
      <div class="page-footer">
        <div class="footer-content">
          <span class="document-title">${documentTitle}</span>
          <span class="page-number">Page ${pageNumber} / ${pages.length}</span>
        </div>
      </div>
    `
  }

  // Fonction pour insérer un saut de page manuel
  const insertPageBreak = () => {
    if (!editor) return
    
    editor.chain().focus().setPageBreak().run()
    
    // Forcer la mise à jour de la pagination après un court délai
    setTimeout(updatePagination, 100)
  }

  // Écouter les changements de contenu
  useEffect(() => {
    if (!editor) return

    const handleUpdate = () => {
      // Debounce pour éviter trop de calculs
      if (updateTimeoutRef.current) {
        clearTimeout(updateTimeoutRef.current)
      }

      updateTimeoutRef.current = setTimeout(updatePagination, 300)
    }

    editor.on('update', handleUpdate)
    editor.on('create', handleUpdate)

    return () => {
      editor.off('update', handleUpdate)
      editor.off('create', handleUpdate)
      
      if (updateTimeoutRef.current) {
        clearTimeout(updateTimeoutRef.current)
      }
    }
  }, [editor])

  // Mise à jour initiale
  useEffect(() => {
    if (editor && containerRef.current) {
      // Délai pour s'assurer que le DOM est prêt
      setTimeout(updatePagination, 500)
    }
  }, [editor])

  return {
    pages,
    currentPage,
    setCurrentPage,
    containerRef,
    generateFooter,
    insertPageBreak,
    updatePagination
  }
}

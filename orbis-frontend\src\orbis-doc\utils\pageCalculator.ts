import { PageSize, PageMargins, PAGE_SIZES, DEFAULT_MARGINS } from '../types'

/**
 * Convertit des millimètres en pixels
 * @param mm Valeur en millimètres
 * @param dpi DPI de l'écran (par défaut 96)
 * @returns Valeur en pixels
 */
export function mmToPx(mm: number, dpi: number = 96): number {
  return (mm * dpi) / 25.4
}

/**
 * Convertit des pixels en millimètres
 * @param px Valeur en pixels
 * @param dpi DPI de l'écran (par défaut 96)
 * @returns Valeur en millimètres
 */
export function pxToMm(px: number, dpi: number = 96): number {
  return (px * 25.4) / dpi
}

/**
 * Calcule la hauteur de page disponible pour le contenu en pixels
 * @param contentHeightMm Hauteur du contenu en mm
 * @param dpi DPI de l'écran
 * @returns Hauteur en pixels
 */
export function calculatePageHeight(contentHeightMm: number, dpi: number = 96): number {
  return mmToPx(contentHeightMm, dpi)
}

/**
 * Calcule les dimensions d'une page en pixels
 * @param pageSize Taille de la page
 * @param margins Marges de la page
 * @param dpi DPI de l'écran
 * @returns Dimensions en pixels
 */
export function calculatePageDimensions(
  pageSize: PageSize = 'A4',
  margins: PageMargins = DEFAULT_MARGINS,
  dpi: number = 96
) {
  const pageDimensions = PAGE_SIZES[pageSize]
  
  return {
    pageWidth: mmToPx(pageDimensions.width, dpi),
    pageHeight: mmToPx(pageDimensions.height, dpi),
    contentWidth: mmToPx(pageDimensions.width - margins.left - margins.right, dpi),
    contentHeight: mmToPx(pageDimensions.height - margins.top - margins.bottom, dpi),
    margins: {
      top: mmToPx(margins.top, dpi),
      right: mmToPx(margins.right, dpi),
      bottom: mmToPx(margins.bottom, dpi),
      left: mmToPx(margins.left, dpi)
    }
  }
}

/**
 * Estime le nombre de pages nécessaires pour un contenu donné
 * @param contentHtml Contenu HTML
 * @param pageSize Taille de la page
 * @param margins Marges de la page
 * @returns Nombre de pages estimé
 */
export function estimatePageCount(
  contentHtml: string,
  pageSize: PageSize = 'A4',
  margins: PageMargins = DEFAULT_MARGINS
): number {
  // Créer un élément temporaire pour mesurer le contenu
  const tempDiv = document.createElement('div')
  tempDiv.style.position = 'absolute'
  tempDiv.style.visibility = 'hidden'
  tempDiv.style.top = '-9999px'
  tempDiv.style.left = '-9999px'
  
  // Appliquer les styles de page
  const { contentWidth, contentHeight } = calculatePageDimensions(pageSize, margins)
  tempDiv.style.width = `${contentWidth}px`
  tempDiv.style.fontSize = '12pt'
  tempDiv.style.lineHeight = '1.5'
  tempDiv.style.fontFamily = 'Arial, sans-serif'
  
  // Insérer le contenu
  tempDiv.innerHTML = contentHtml
  
  document.body.appendChild(tempDiv)
  
  try {
    const actualHeight = tempDiv.scrollHeight
    const pageCount = Math.max(1, Math.ceil(actualHeight / contentHeight))
    return pageCount
  } finally {
    document.body.removeChild(tempDiv)
  }
}

/**
 * Calcule la position d'un élément dans le document paginé
 * @param elementTop Position top de l'élément
 * @param pageHeight Hauteur d'une page
 * @returns Numéro de page et position relative
 */
export function calculateElementPagePosition(
  elementTop: number,
  pageHeight: number
): { page: number; relativeTop: number } {
  const page = Math.floor(elementTop / pageHeight) + 1
  const relativeTop = elementTop % pageHeight
  
  return { page, relativeTop }
}

/**
 * Génère les styles CSS pour une page donnée
 * @param pageSize Taille de la page
 * @param margins Marges de la page
 * @param showShadows Afficher les ombres
 * @returns Objet de styles CSS
 */
export function generatePageStyles(
  pageSize: PageSize = 'A4',
  margins: PageMargins = DEFAULT_MARGINS,
  showShadows: boolean = true
): React.CSSProperties {
  const pageDimensions = PAGE_SIZES[pageSize]
  
  return {
    width: `${pageDimensions.width}mm`,
    minHeight: `${pageDimensions.height}mm`,
    padding: `${margins.top}mm ${margins.right}mm ${margins.bottom}mm ${margins.left}mm`,
    backgroundColor: 'white',
    boxShadow: showShadows ? '0 4px 8px rgba(0, 0, 0, 0.1)' : 'none',
    margin: '0 auto 24px',
    position: 'relative' as const,
  }
}

/**
 * Calcule l'espace disponible restant sur une page
 * @param currentHeight Hauteur actuelle du contenu
 * @param pageHeight Hauteur totale de la page
 * @returns Espace restant en pixels
 */
export function calculateRemainingSpace(
  currentHeight: number,
  pageHeight: number
): number {
  const pagePosition = currentHeight % pageHeight
  return pageHeight - pagePosition
}

/**
 * Détermine si un élément doit être déplacé sur la page suivante
 * @param elementHeight Hauteur de l'élément
 * @param remainingSpace Espace restant sur la page
 * @param threshold Seuil minimum (par défaut 20% de l'élément)
 * @returns True si l'élément doit être déplacé
 */
export function shouldMoveToNextPage(
  elementHeight: number,
  remainingSpace: number,
  threshold: number = 0.2
): boolean {
  return remainingSpace < elementHeight * threshold
}

import { useState, useCallback, useEffect } from 'react'
import { useEditorSetup } from './useEditorSetup'
import { useAutoSave } from './useAutoSave'
import { OrbisDocConfig, OrbisDocContent, OrbisDocCoverPage } from '../types'

export function useOrbisDoc(
  config: OrbisDocConfig,
  initialContent?: OrbisDocContent
) {
  const [content, setContent] = useState<OrbisDocContent | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)
  const [showCoverPageModal, setShowCoverPageModal] = useState(false)

  // Configuration de l'éditeur
  const { editor } = useEditorSetup({
    config,
    initialContent: initialContent?.html || '',
    onChange: handleContentChange,
  })

  // Gérer les changements de contenu
  function handleContentChange(html: string) {
    if (!editor) return

    const json = editor.getJSON()
    const wordCount = editor.storage.characterCount?.words() || 0
    const characterCount = editor.storage.characterCount?.characters() || 0

    const newContent: OrbisDocContent = {
      html,
      json,
      pages: [], // Sera calculé par le hook de pagination
      totalPages: 0,
      wordCount,
      characterCount,
      coverPage: content?.coverPage,
    }

    setContent(newContent)
    setHasUnsavedChanges(true)

    if (config.onChange) {
      config.onChange(newContent)
    }
  }

  // Sauvegarder le document
  const saveDocument = useCallback(async () => {
    if (!content || !config.onSave) return

    try {
      setIsSaving(true)
      await config.onSave(content)
      setHasUnsavedChanges(false)
    } catch (error) {
      console.error('Erreur lors de la sauvegarde:', error)
      throw error
    } finally {
      setIsSaving(false)
    }
  }, [content, config])

  // Auto-sauvegarde
  useAutoSave({
    editor,
    config,
    onSave: saveDocument,
    hasUnsavedChanges,
  })

  // Insérer un saut de page
  const insertPageBreak = useCallback(() => {
    if (!editor) return
    editor.commands.insertPageBreak()
  }, [editor])

  // Insérer une page de garde
  const insertCoverPage = useCallback((coverPageData: OrbisDocCoverPage) => {
    if (!editor) return

    // Insérer la page de garde au début du document
    editor.commands.insertContentAt(0, {
      type: 'coverPage',
      attrs: coverPageData,
    })

    // Mettre à jour le contenu
    if (content) {
      const updatedContent = {
        ...content,
        coverPage: coverPageData,
      }
      setContent(updatedContent)
    }

    setShowCoverPageModal(false)
  }, [editor, content])

  // Mettre à jour le contenu
  const updateContent = useCallback((newContent: Partial<OrbisDocContent>) => {
    if (!content) return

    const updatedContent = {
      ...content,
      ...newContent,
    }

    setContent(updatedContent)
    
    if (newContent.html && editor) {
      editor.commands.setContent(newContent.html)
    }
  }, [content, editor])

  // Initialisation
  useEffect(() => {
    if (editor) {
      setIsLoading(false)
      
      if (initialContent) {
        setContent(initialContent)
      } else {
        // Créer un contenu vide
        const emptyContent: OrbisDocContent = {
          html: '',
          json: editor.getJSON(),
          pages: [],
          totalPages: 0,
          wordCount: 0,
          characterCount: 0,
        }
        setContent(emptyContent)
      }
    }
  }, [editor, initialContent])

  return {
    editor,
    content,
    isLoading,
    isSaving,
    hasUnsavedChanges,
    showCoverPageModal,
    setShowCoverPageModal,
    updateContent,
    saveDocument,
    insertPageBreak,
    insertCoverPage,
  }
}

import { OrbisDocConfig, OrbisDocFooter } from '../types'

export interface FooterGenerationOptions {
  pageNumber: number
  totalPages: number
  config: OrbisDocConfig
  customFooter?: OrbisDocFooter
  isCoverPage?: boolean
  isBlankPage?: boolean
}

/**
 * Génère le HTML du pied de page pour une page donnée
 */
export function generateFooter({
  pageNumber,
  totalPages,
  config,
  customFooter,
  isCoverPage = false,
  isBlankPage = false
}: FooterGenerationOptions): string {
  // Pas de pied de page pour les pages de garde et pages vierges
  if (isCoverPage || isBlankPage) {
    return ''
  }

  const footer = customFooter || getDefaultFooter(config)
  
  // Remplacer les variables dans le template
  const leftContent = replaceVariables(footer.left || '', {
    pageNumber,
    totalPages,
    config
  })
  
  const centerContent = replaceVariables(footer.center || '', {
    pageNumber,
    totalPages,
    config
  })
  
  const rightContent = replaceVariables(footer.right || '', {
    pageNumber,
    totalPages,
    config
  })

  // Utiliser un template personnalisé si fourni
  if (footer.customTemplate) {
    return replaceVariables(footer.customTemplate, {
      pageNumber,
      totalPages,
      config,
      leftContent,
      centerContent,
      rightContent
    })
  }

  // Template par défaut
  return `
    <div class="footer-content">
      <div class="footer-left">${leftContent}</div>
      <div class="footer-center">${centerContent}</div>
      <div class="footer-right">${rightContent}</div>
    </div>
  `
}

/**
 * Obtient la configuration de pied de page par défaut
 */
function getDefaultFooter(config: OrbisDocConfig): OrbisDocFooter {
  return {
    left: config.documentTitle,
    center: `${config.documentType}${config.documentIndice ? ` - Indice ${config.documentIndice}` : ''}`,
    right: 'Page {pageNumber} / {totalPages}',
    showPageNumber: true
  }
}

/**
 * Remplace les variables dans un template de texte
 */
function replaceVariables(
  template: string,
  variables: {
    pageNumber: number
    totalPages: number
    config: OrbisDocConfig
    leftContent?: string
    centerContent?: string
    rightContent?: string
  }
): string {
  const { pageNumber, totalPages, config } = variables
  
  return template
    .replace(/\{pageNumber\}/g, pageNumber.toString())
    .replace(/\{totalPages\}/g, totalPages.toString())
    .replace(/\{documentTitle\}/g, config.documentTitle)
    .replace(/\{documentType\}/g, config.documentType)
    .replace(/\{documentIndice\}/g, config.documentIndice || '')
    .replace(/\{date\}/g, new Date().toLocaleDateString('fr-FR'))
    .replace(/\{time\}/g, new Date().toLocaleTimeString('fr-FR'))
    .replace(/\{leftContent\}/g, variables.leftContent || '')
    .replace(/\{centerContent\}/g, variables.centerContent || '')
    .replace(/\{rightContent\}/g, variables.rightContent || '')
}

/**
 * Génère des templates de pieds de page prédéfinis
 */
export const footerTemplates = {
  default: {
    name: 'Par défaut',
    description: 'Titre à gauche, type au centre, numérotation à droite',
    template: {
      left: '{documentTitle}',
      center: '{documentType}',
      right: 'Page {pageNumber} / {totalPages}',
      showPageNumber: true
    }
  },
  
  minimal: {
    name: 'Minimal',
    description: 'Seulement la numérotation des pages',
    template: {
      left: '',
      center: '',
      right: 'Page {pageNumber} / {totalPages}',
      showPageNumber: true
    }
  },
  
  detailed: {
    name: 'Détaillé',
    description: 'Informations complètes du document',
    template: {
      left: '{documentTitle}',
      center: '{documentType} - Indice {documentIndice}',
      right: 'Page {pageNumber} / {totalPages} - {date}',
      showPageNumber: true
    }
  },
  
  centered: {
    name: 'Centré',
    description: 'Toutes les informations au centre',
    template: {
      left: '',
      center: '{documentTitle} - Page {pageNumber} / {totalPages}',
      right: '',
      showPageNumber: true
    }
  },
  
  corporate: {
    name: 'Entreprise',
    description: 'Style professionnel avec séparateurs',
    customTemplate: `
      <div class="footer-content corporate-footer">
        <div class="footer-section">
          <span class="footer-title">{documentTitle}</span>
          <span class="footer-separator">|</span>
          <span class="footer-type">{documentType}</span>
        </div>
        <div class="footer-section">
          <span class="footer-page">Page {pageNumber} / {totalPages}</span>
          <span class="footer-separator">|</span>
          <span class="footer-date">{date}</span>
        </div>
      </div>
    `
  },
  
  technical: {
    name: 'Technique',
    description: 'Optimisé pour les documents techniques',
    customTemplate: `
      <div class="footer-content technical-footer">
        <div class="footer-left">
          <div class="doc-ref">{documentType} - {documentIndice}</div>
          <div class="doc-title">{documentTitle}</div>
        </div>
        <div class="footer-right">
          <div class="page-info">Page {pageNumber} / {totalPages}</div>
          <div class="date-info">{date}</div>
        </div>
      </div>
    `
  }
}

/**
 * Calcule la hauteur du pied de page en fonction du contenu
 */
export function calculateFooterHeight(footerContent: string): number {
  // Créer un élément temporaire pour mesurer la hauteur
  const tempDiv = document.createElement('div')
  tempDiv.style.position = 'absolute'
  tempDiv.style.visibility = 'hidden'
  tempDiv.style.width = '170mm' // Largeur A4 moins marges
  tempDiv.style.fontSize = '10pt'
  tempDiv.className = 'page-footer'
  tempDiv.innerHTML = footerContent
  
  document.body.appendChild(tempDiv)
  
  try {
    const height = tempDiv.offsetHeight
    return height
  } finally {
    document.body.removeChild(tempDiv)
  }
}

/**
 * Valide la configuration d'un pied de page
 */
export function validateFooterConfig(footer: OrbisDocFooter): {
  isValid: boolean
  errors: string[]
} {
  const errors: string[] = []
  
  // Vérifier que au moins un contenu est défini
  if (!footer.left && !footer.center && !footer.right && !footer.customTemplate) {
    errors.push('Au moins un contenu de pied de page doit être défini')
  }
  
  // Vérifier la longueur des contenus
  const maxLength = 100
  if (footer.left && footer.left.length > maxLength) {
    errors.push(`Le contenu gauche ne peut pas dépasser ${maxLength} caractères`)
  }
  if (footer.center && footer.center.length > maxLength) {
    errors.push(`Le contenu central ne peut pas dépasser ${maxLength} caractères`)
  }
  if (footer.right && footer.right.length > maxLength) {
    errors.push(`Le contenu droit ne peut pas dépasser ${maxLength} caractères`)
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * Génère un aperçu du pied de page
 */
export function generateFooterPreview(
  footer: OrbisDocFooter,
  config: OrbisDocConfig
): string {
  return generateFooter({
    pageNumber: 1,
    totalPages: 10,
    config,
    customFooter: footer
  })
}

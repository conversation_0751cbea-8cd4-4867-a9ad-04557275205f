'use client'

import React from 'react'

interface PageNavigatorProps {
  currentPage: number
  totalPages: number
  onPageChange: (page: number) => void
  onPreviousPage: () => void
  onNextPage: () => void
}

export default function PageNavigator({
  currentPage,
  totalPages,
  onPageChange,
  onPreviousPage,
  onNextPage
}: PageNavigatorProps) {
  
  return (
    <div className="page-navigator bg-white border-b border-gray-200 p-3">
      <div className="flex items-center justify-center space-x-4">
        {/* Bouton page précédente */}
        <button
          onClick={onPreviousPage}
          disabled={currentPage <= 1}
          className="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          ← Précédente
        </button>

        {/* Indicateur de page */}
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-600">Page</span>
          <input
            type="number"
            min="1"
            max={totalPages}
            value={currentPage}
            onChange={(e) => {
              const page = parseInt(e.target.value)
              if (page >= 1 && page <= totalPages) {
                onPageChange(page)
              }
            }}
            className="w-16 px-2 py-1 text-sm text-center border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <span className="text-sm text-gray-600">/ {totalPages}</span>
        </div>

        {/* Bouton page suivante */}
        <button
          onClick={onNextPage}
          disabled={currentPage >= totalPages}
          className="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Suivante →
        </button>
      </div>
    </div>
  )
}

'use client'

import React, { useEffect, useRef } from 'react'
import { EditorContent } from '@tiptap/react'
import { useOrbisDoc } from '../hooks/useOrbisDoc'
import { useDocumentPagination } from '../hooks/useDocumentPagination'
import { OrbisDocConfig, OrbisDocContent } from '../types'
import { generateHarmoniaCoverPageHTML } from '@/services/harmoniaPageService'
import { FastAuthService } from '@/lib/auth'
import EditorToolbar from './EditorToolbar'
import DocumentPages from './DocumentPages'
import PageNavigator from './PageNavigator'
// import CoverPageModal from './CoverPageModal' // Supprimé - Génération directe HARMONIA
import '../styles/orbis-doc.css'

interface OrbisDocEditorProps {
  config: OrbisDocConfig
  initialContent?: OrbisDocContent
  className?: string
}

export default function OrbisDocEditor({
  config,
  initialContent,
  className = ''
}: OrbisDocEditorProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  
  // Hook principal de l'éditeur
  const {
    editor,
    content,
    isLoading,
    isSaving,
    hasUnsavedChanges,
    updateContent,
    saveDocument,
    insertPageBreak,
    insertCoverPage,
    showCoverPageModal,
    setShowCoverPageModal
  } = useOrbisDoc(config, initialContent)

  // Hook de pagination
  const {
    pages,
    currentPage,
    totalPages,
    goToPage,
    nextPage,
    previousPage,
    updatePagination
  } = useDocumentPagination(editor, config)

  // Mise à jour de la pagination quand le contenu change
  useEffect(() => {
    if (editor && content) {
      updatePagination()
    }
  }, [editor, content, updatePagination])

  // Callback pour les changements de page
  useEffect(() => {
    if (config.onPageChange) {
      config.onPageChange(currentPage)
    }
  }, [currentPage, config])

  if (isLoading) {
    return (
      <div className="orbis-doc-loading">
        <div className="loading-spinner">
          <div className="spinner"></div>
          <p>Chargement de l'éditeur...</p>
        </div>
      </div>
    )
  }

  if (!editor) {
    return (
      <div className="orbis-doc-error">
        <p>Erreur lors du chargement de l'éditeur</p>
      </div>
    )
  }

  return (
    <div className={`orbis-doc-editor ${className}`}>
      {/* Barre d'outils principale */}
      <EditorToolbar
        editor={editor}
        config={config}
        isSaving={isSaving}
        hasUnsavedChanges={hasUnsavedChanges}
        onSave={saveDocument}
        onInsertPageBreak={insertPageBreak}
        onInsertCoverPage={async () => {
          console.log('🎯 Génération directe page de garde HARMONIA depuis OrbisDocEditor')

          try {
            // Récupérer le lotId depuis la config
            const lotId = config.lotId || 1 // Utiliser le lotId de la config ou 1 par défaut

            // Récupérer les données du lot
            const lotResponse = await FastAuthService.makeAuthenticatedRequest(`/lots/${lotId}`)
            if (!lotResponse.ok) throw new Error('Erreur récupération lot')
            const lot = await lotResponse.json()

            // Récupérer les stakeholders
            const stakeholdersResponse = await FastAuthService.makeAuthenticatedRequest(`/lots/${lotId}/stakeholders`)
            if (!stakeholdersResponse.ok) throw new Error('Erreur récupération stakeholders')
            const stakeholders = await stakeholdersResponse.json()

            // Préparer les données pour HARMONIA
            const coverPageData = {
              lot: {
                id: lot.id,
                name: lot.name,
                code: lot.code,
                description: lot.description,
                current_phase: lot.current_phase,
                photo_url: lot.photo_url,
                project: lot.project
              },
              documentType: config.documentType || 'CCTP',
              documentIndice: config.documentIndice || '01',
              companies: stakeholders
                .filter((s: any) => s.company)
                .map((s: any) => ({
                  id: s.company.id,
                  name: s.company.company_name,
                  role: s.role || 'ENT',
                  activity: s.company.activity,
                  address: s.company.address,
                  phone: s.company.phone,
                  email: s.company.email,
                  logo_url: s.company.logo_url
                }))
            }

            // Générer le HTML HARMONIA
            const coverPageHTML = generateHarmoniaCoverPageHTML(coverPageData)

            // Vider le contenu et insérer la page de garde
            if (editor) {
              editor.commands.clearContent()
              if (editor.commands.insertCoverPage) {
                editor.commands.insertCoverPage(coverPageHTML)
              } else {
                editor.commands.setContent(coverPageHTML)
              }
              console.log('✅ PAGE DE GARDE HARMONIA CRÉÉE!')
            }

          } catch (error) {
            console.error('❌ Erreur génération page de garde:', error)
            alert('Erreur lors de la génération de la page de garde')
          }
        }}
      />

      {/* Zone principale d'édition */}
      <div className="orbis-doc-main" ref={containerRef}>
        {/* Navigation des pages */}
        <PageNavigator
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={goToPage}
          onPreviousPage={previousPage}
          onNextPage={nextPage}
        />

        {/* Pages du document */}
        <DocumentPages
          editor={editor}
          pages={pages}
          config={config}
          currentPage={currentPage}
        />
      </div>

      {/* Modal supprimée - Génération directe de page de garde HARMONIA */}
    </div>
  )
}

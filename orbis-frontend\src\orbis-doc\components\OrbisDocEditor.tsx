'use client'

import React, { useEffect, useRef } from 'react'
import { EditorContent } from '@tiptap/react'
import { useOrbisDoc } from '../hooks/useOrbisDoc'
import { useDocumentPagination } from '../hooks/useDocumentPagination'
import { OrbisDocConfig, OrbisDocContent } from '../types'
import EditorToolbar from './EditorToolbar'
import DocumentPages from './DocumentPages'
import PageNavigator from './PageNavigator'
import CoverPageModal from './CoverPageModal'
import '../styles/orbis-doc.css'

interface OrbisDocEditorProps {
  config: OrbisDocConfig
  initialContent?: OrbisDocContent
  className?: string
}

export default function OrbisDocEditor({
  config,
  initialContent,
  className = ''
}: OrbisDocEditorProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  
  // Hook principal de l'éditeur
  const {
    editor,
    content,
    isLoading,
    isSaving,
    hasUnsavedChanges,
    updateContent,
    saveDocument,
    insertPageBreak,
    insertCoverPage,
    showCoverPageModal,
    setShowCoverPageModal
  } = useOrbisDoc(config, initialContent)

  // Hook de pagination
  const {
    pages,
    currentPage,
    totalPages,
    goToPage,
    nextPage,
    previousPage,
    updatePagination
  } = useDocumentPagination(editor, config)

  // Mise à jour de la pagination quand le contenu change
  useEffect(() => {
    if (editor && content) {
      updatePagination()
    }
  }, [editor, content, updatePagination])

  // Callback pour les changements de page
  useEffect(() => {
    if (config.onPageChange) {
      config.onPageChange(currentPage)
    }
  }, [currentPage, config])

  if (isLoading) {
    return (
      <div className="orbis-doc-loading">
        <div className="loading-spinner">
          <div className="spinner"></div>
          <p>Chargement de l'éditeur...</p>
        </div>
      </div>
    )
  }

  if (!editor) {
    return (
      <div className="orbis-doc-error">
        <p>Erreur lors du chargement de l'éditeur</p>
      </div>
    )
  }

  return (
    <div className={`orbis-doc-editor ${className}`}>
      {/* Barre d'outils principale */}
      <EditorToolbar
        editor={editor}
        config={config}
        isSaving={isSaving}
        hasUnsavedChanges={hasUnsavedChanges}
        onSave={saveDocument}
        onInsertPageBreak={insertPageBreak}
        onInsertCoverPage={() => setShowCoverPageModal(true)}
      />

      {/* Zone principale d'édition */}
      <div className="orbis-doc-main" ref={containerRef}>
        {/* Navigation des pages */}
        <PageNavigator
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={goToPage}
          onPreviousPage={previousPage}
          onNextPage={nextPage}
        />

        {/* Pages du document */}
        <DocumentPages
          editor={editor}
          pages={pages}
          config={config}
          currentPage={currentPage}
        />
      </div>

      {/* Modal de création de page de garde */}
      {showCoverPageModal && (
        <CoverPageModal
          isOpen={showCoverPageModal}
          onClose={() => setShowCoverPageModal(false)}
          onGenerate={insertCoverPage}
          config={config}
        />
      )}
    </div>
  )
}

import React from 'react'
import { NodeViewWrapper } from '@tiptap/react'
import { useCoverPageGeneration } from '../hooks/useCoverPageGeneration'
import '../styles/cover-page-node.css'

interface CoverPageNodeViewProps {
  node: {
    attrs: {
      content: string
      class?: string
    }
  }
  updateAttributes: (attributes: Record<string, any>) => void
  selected: boolean
  editor?: any
  getPos: () => number
}

const CoverPageNodeView: React.FC<CoverPageNodeViewProps> = ({
  node,
  updateAttributes,
  selected,
  editor,
  getPos,
}) => {
  const { content } = node.attrs

  // Hook pour la génération de page de garde (seulement si editor disponible)
  const { generateCoverPage, isGenerating } = useCoverPageGeneration({
    editor: editor || null,
    lotId: 1, // TODO: récupérer le vrai lotId depuis le contexte
    documentType: 'CCTP',
    documentIndice: '01'
  })

  // Fonction pour régénérer la page de garde
  const handleRegenerate = async () => {
    if (!editor) {
      console.warn('Éditeur non disponible pour la régénération')
      return
    }

    try {
      // Régénérer et remplacer le contenu du nœud
      const newContent = await generateCoverPage()
      if (newContent) {
        updateAttributes({ content: newContent })
      }
    } catch (error) {
      console.error('Erreur lors de la régénération:', error)
    }
  }

  // Fonction pour éditer la page de garde
  const handleEdit = () => {
    if (!editor || !getPos) {
      console.warn('Éditeur ou position non disponible pour l\'édition')
      return
    }

    // Convertir le nœud en contenu éditable
    try {
      const pos = getPos()
      editor.chain()
        .focus()
        .setTextSelection(pos)
        .deleteSelection()
        .insertContent(content)
        .run()
    } catch (error) {
      console.error('Erreur lors de la conversion en mode édition:', error)
    }
  }

  return (
    <NodeViewWrapper
      className={`cover-page-wrapper ${selected ? 'ProseMirror-selectednode' : ''}`}
      data-type="cover-page"
    >
      <div className="cover-page-container">
        {/* Indicateur de sélection */}
        {selected && (
          <div className="cover-page-selected-indicator">
            📄 Page de garde sélectionnée
          </div>
        )}
        
        {/* Contenu de la page de garde */}
        <div 
          className="cover-page-content"
          dangerouslySetInnerHTML={{ __html: content }}
        />
        
        {/* Overlay pour empêcher l'édition directe */}
        <div className="cover-page-overlay">
          <div className="cover-page-controls">
            <button
              onClick={handleRegenerate}
              disabled={isGenerating}
              className="cover-page-button"
              title="Régénérer la page de garde avec les données actuelles"
            >
              {isGenerating ? '⏳' : '🔄'} Régénérer
            </button>
            <button
              onClick={handleEdit}
              className="cover-page-button"
              title="Convertir en contenu éditable"
            >
              ✏️ Éditer
            </button>
          </div>
        </div>
      </div>
    </NodeViewWrapper>
  )
}

export default CoverPageNodeView

/**
 * Service de génération de page de garde SIMPLE et FONCTIONNEL
 * Génère directement le HTML sans système de template complexe
 */

export interface CoverPageData {
  lot: {
    id: number
    name: string
    code?: string
    description?: string
    current_phase?: string
    photo_url?: string
    project?: {
      id: number
      name: string
      address?: string
      photo_url?: string
    }
  }
  documentType: string
  documentIndice: string
  companies: Array<{
    id: number
    name: string
    role: string
    activity?: string
    address?: string
    phone?: string
    email?: string
    logo_url?: string | null
  }>
}

export function generateSimpleCoverPageHTML(data: CoverPageData): string {
  console.log('🎯 Génération SIMPLE de page de garde')
  console.log('📊 Données:', data)

  // Préparer les données
  const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'
  
  // Trouver les entreprises par rôle
  const moaCompany = data.companies.find(c => c.role === 'MOA')
  const otherCompanies = data.companies.filter(c => c.role !== 'MOA')

  // Titre du document
  const getDocumentTitle = (type: string): string => {
    switch (type.toUpperCase()) {
      case 'CCTP': return 'CAHIER DES CLAUSES TECHNIQUES PARTICULIÈRES'
      case 'DPGF': return 'DÉCOMPOSITION DU PRIX GLOBAL ET FORFAITAIRE'
      default: return type.toUpperCase()
    }
  }

  // Traduction des rôles en français
  const getRoleDisplayName = (role: string): string => {
    const roleNames: Record<string, string> = {
      'MOA': 'Maître d\'ouvrage',
      'MOADEL': 'Maître d\'ouvrage délégué',
      'ARCHI': 'Architecte',
      'BE': 'Bureau d\'études',
      'BC': 'Bureau de contrôle',
      'OPC': 'Coordinateur OPC',
      'ENT': 'Entreprise',
      'FO': 'Fournisseur'
    }
    return roleNames[role] || role
  }

  // Générer la liste des entreprises (colonne gauche) avec classes CSS
  const companiesHTML = otherCompanies.map(company => {
    console.log('🏢 Company data:', company) // Debug pour voir les données
    return `
    <table class="entreprise-item">
      <tr>
        <td class="logo-cell">
          ${company.logo_url ? `<img src="${baseUrl}${company.logo_url}" alt="Logo" />` : '<div class="logo-placeholder"></div>'}
        </td>
        <td>
          <p class="role">
            ${getRoleDisplayName(company.role)} :
          </p>
          <p class="nom">
            ${company.name}
          </p>
          ${company.activity ? `<p class="activite">${company.activity}</p>` : ''}
          ${company.address ? `<p class="adresse">${company.address}</p>` : ''}
          ${company.phone ? `<p class="contact">Tél : ${company.phone}</p>` : ''}
          ${company.email ? `<p class="contact">Mail : ${company.email}</p>` : ''}
        </td>
      </tr>
    </table>
  `}).join('')

  // Générer les infos du projet (colonne droite) avec classes CSS
  const projectHTML = `
    <table>
      <tr>
        <td>
          <p class="titre-projet">
            ${data.lot.project?.name || 'Projet non défini'}
          </p>
          ${data.lot.project?.address ? `<p class="adresse-projet">${data.lot.project.address}</p>` : ''}
        </td>
      </tr>
    </table>

    ${data.lot.photo_url || data.lot.project?.photo_url ? `
      <table>
        <tr>
          <td>
            <img src="${baseUrl}${data.lot.photo_url || data.lot.project?.photo_url}"
                 class="image-projet"
                 alt="Image du projet" />
          </td>
        </tr>
      </table>
    ` : ''}

    ${moaCompany ? `
      <table class="bordered">
        <tr>
          <td>
            <p class="moa-titre">
              MAÎTRE D'OUVRAGE
            </p>
          </td>
        </tr>
        <tr>
          <td>
            <table>
              <tr>
                <td>
                  ${moaCompany.logo_url ? `<img src="${baseUrl}${moaCompany.logo_url}" alt="Logo MOA" />` : ''}
                </td>
                <td>
                  <p class="moa-nom">
                    ${moaCompany.name}
                  </p>
                  ${moaCompany.address ? `<p class="moa-adresse">${moaCompany.address}</p>` : ''}
                </td>
              </tr>
            </table>
          </td>
        </tr>
      </table>
    ` : ''}

    <table class="lot-container">
      <tr>
        <td>
          <p class="lot-numero">
            Lot n°${data.lot.code || data.lot.id}
          </p>
          <p class="lot-description">
            ${data.lot.description || data.lot.name}
          </p>
        </td>
      </tr>
    </table>

    <table class="document-container">
      <tr>
        <td>
          <p class="document-titre">
            ${getDocumentTitle(data.documentType)}
          </p>
        </td>
      </tr>
    </table>

    <table class="info-table">
      <tr>
        <td class="label">Dossier</td>
        <td class="value">001</td>
      </tr>
      <tr>
        <td class="label">Date</td>
        <td class="value">${new Date().toLocaleDateString('fr-FR')}</td>
      </tr>
      <tr>
        <td class="label">Phase</td>
        <td class="value phase">${data.lot.current_phase || 'ESQ'}</td>
      </tr>
      <tr>
        <td class="label">Indice</td>
        <td class="value">${data.documentIndice}</td>
      </tr>
    </table>
  `

  // HTML final avec layout tableau - Page de garde unique
  const html = `
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <style>
    body {
      margin: 0;
      padding: 20px;
      font-family: Arial, sans-serif;
      background-color: #f5f5f5;
    }

    .pagedegarde {
      margin: 0 auto;
      background-color: transparent;
      border: none;
      width: 210mm;
      height: 297mm;
      padding: 0;
      position: relative;
      box-sizing: border-box;
      font-family: Arial, sans-serif;
      page-break-after: always;
      overflow: hidden;
    }

    .pagedegarde table {
      border-collapse: collapse;
      width: 100%;
      height: 100%;
      table-layout: fixed;
    }

    .pagedegarde p {
      margin: 0;
      line-height: 1.3;
    }

    /* Colonne gauche - Entreprises */
    .pagedegarde .col-entreprises {
      width: 45%;
      vertical-align: top;
      padding: 8px;
      background-color: #0F766E;
      height: 100%;
    }

    .pagedegarde .col-entreprises .entreprise-item {
      width: 100%;
      margin-bottom: 6px;
      border-bottom: 1px solid rgba(255,255,255,0.3);
      padding-bottom: 4px;
      display: table;
      table-layout: fixed;
    }

    .pagedegarde .col-entreprises .logo-cell {
      width: 50px;
      vertical-align: middle;
      padding-right: 6px;
      display: table-cell;
    }

    .pagedegarde .col-entreprises .logo-cell img {
      height: 40px;
      width: auto;
      max-width: 40px;
      border: 1px solid rgba(255,255,255,0.5);
      background-color: white;
      padding: 1px;
      object-fit: contain;
      display: block;
    }

    .pagedegarde .col-entreprises .logo-placeholder {
      width: 40px;
      height: 40px;
      background-color: rgba(255,255,255,0.2);
      border: 1px solid rgba(255,255,255,0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 8px;
      color: rgba(255,255,255,0.7);
    }

    .pagedegarde .col-entreprises .info-cell {
      vertical-align: middle;
      color: white;
      font-size: 8px;
      line-height: 1.2;
      display: table-cell;
    }

    .pagedegarde .col-entreprises .role {
      font-size: 8px;
      font-weight: bold;
      color: white;
      margin-bottom: 1px;
    }

    .pagedegarde .col-entreprises .nom {
      font-size: 9px;
      font-weight: bold;
      color: white;
      margin-bottom: 1px;
    }

    .pagedegarde .col-entreprises .activite {
      font-size: 7px;
      color: rgba(255,255,255,0.9);
      font-style: italic;
      margin-bottom: 1px;
    }

    .pagedegarde .col-entreprises .adresse {
      font-size: 7px;
      color: rgba(255,255,255,0.9);
      margin-bottom: 1px;
    }

    .pagedegarde .col-entreprises .contact {
      font-size: 7px;
      color: rgba(255,255,255,0.9);
    }

    /* Colonne droite - Projet */
    .pagedegarde .col-projet {
      width: 55%;
      vertical-align: top;
      padding: 8px;
      background-color: white;
      height: 100%;
    }

    .pagedegarde .col-projet .titre-projet {
      font-size: 12px;
      font-weight: bold;
      margin-bottom: 3px;
      text-align: center;
    }

    .pagedegarde .col-projet .adresse-projet {
      font-size: 9px;
      margin-bottom: 6px;
      font-weight: 500;
      text-align: center;
    }

    .pagedegarde .col-projet .image-projet {
      width: 100%;
      max-height: 80px;
      object-fit: cover;
      border: 1px solid #ccc;
      margin-bottom: 6px;
    }

    .pagedegarde .col-projet .moa-titre {
      font-size: 11px;
      font-weight: bold;
      margin-bottom: 6px;
      text-align: center;
    }

    .pagedegarde .col-projet .moa-nom {
      font-size: 10px;
      font-weight: bold;
      margin-bottom: 2px;
    }

    .pagedegarde .col-projet .moa-adresse {
      font-size: 9px;
      color: #666;
    }

    .pagedegarde .col-projet .lot-numero {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 6px;
      color: #333;
      text-align: center;
    }

    .pagedegarde .col-projet .lot-description {
      font-size: 13px;
      font-weight: bold;
      text-transform: uppercase;
      letter-spacing: 0.8px;
      color: #555;
      text-align: center;
    }

    .pagedegarde .col-projet .document-titre {
      font-size: 12px;
      font-weight: bold;
      letter-spacing: 0.3px;
      text-align: center;
      margin-bottom: 6px;
    }

    /* Tableaux d'informations */
    .pagedegarde .info-table {
      border: 1px solid #000;
      font-size: 8px;
      margin-top: 4px;
      margin-bottom: 4px;
    }

    .pagedegarde .info-table td {
      padding: 3px;
      border: 1px solid #000;
    }

    .pagedegarde .info-table .label {
      font-weight: bold;
      background-color: #e8e8e8;
      width: 40%;
    }

    .pagedegarde .info-table .value {
      text-align: center;
      font-weight: bold;
    }

    .pagedegarde .info-table .phase {
      color: #0066cc;
    }

    /* Conteneurs avec bordures */
    .pagedegarde .bordered {
      border: 2px solid #000;
      margin-bottom: 15px;
      background-color: #fafafa;
    }

    .pagedegarde .lot-container {
      border: 1px solid #ccc;
      background-color: #f9f9f9;
      margin-bottom: 15px;
    }

    .pagedegarde .document-container {
      border: 2px solid #000;
      border-radius: 12px;
      background-color: #f5f5f5;
      margin-bottom: 15px;
    }

    /* Indicateur de version */
    .pagedegarde .version-indicator {
      position: absolute;
      top: 5px;
      left: 5px;
      font-size: 8px;
      color: #0F766E;
      font-weight: bold;
      background-color: rgba(15, 118, 110, 0.1);
      padding: 2px 5px;
      border-radius: 3px;
    }
  </style>
</head>
<body>
  <div class="pagedegarde">
    <p class="version-indicator">
      � CSS Classes
    </p>

    <table>
      <tr>
        <td class="col-entreprises">
          ${companiesHTML}
        </td>
        <td class="col-projet">
          ${projectHTML}
        </td>
      </tr>
    </table>
  </div>
</body>
</html>`

  console.log('✅ HTML généré avec succès (version simple)')
  return html
}

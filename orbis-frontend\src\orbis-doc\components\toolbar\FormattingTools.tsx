'use client'

import React from 'react'
import { Editor } from '@tiptap/react'

interface FormattingToolsProps {
  editor: Editor
}

export default function FormattingTools({ editor }: FormattingToolsProps) {
  return (
    <div className="flex items-center space-x-1 border-r border-gray-300 pr-3 mr-3">
      {/* Formatage de base */}
      <div className="flex items-center space-x-1">
        <button
          onClick={() => editor.chain().focus().toggleBold().run()}
          className={`p-2 rounded hover:bg-gray-100 transition-colors ${
            editor.isActive('bold') ? 'bg-blue-100 text-blue-700' : 'text-gray-700'
          }`}
          title="Gras (Ctrl+B)"
        >
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path d="M3 4a1 1 0 011-1h6a4 4 0 014 4v.5a4.5 4.5 0 01-2.4 3.99A5 5 0 0115 16v1a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm8 7V7a2 2 0 00-2-2H5v4h4a2 2 0 002-2zm-2 2H5v4h6a3 3 0 000-6z" />
          </svg>
        </button>
        
        <button
          onClick={() => editor.chain().focus().toggleItalic().run()}
          className={`p-2 rounded hover:bg-gray-100 transition-colors ${
            editor.isActive('italic') ? 'bg-blue-100 text-blue-700' : 'text-gray-700'
          }`}
          title="Italique (Ctrl+I)"
        >
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path d="M8 1a1 1 0 011 1v1h2a1 1 0 110 2h-.5l-1.6 8H11a1 1 0 110 2H7a1 1 0 01-1-1v-1H4a1 1 0 110-2h.5l1.6-8H5a1 1 0 110-2h2V2a1 1 0 011-1z" />
          </svg>
        </button>
        
        <button
          onClick={() => editor.chain().focus().toggleUnderline().run()}
          className={`p-2 rounded hover:bg-gray-100 transition-colors ${
            editor.isActive('underline') ? 'bg-blue-100 text-blue-700' : 'text-gray-700'
          }`}
          title="Souligné (Ctrl+U)"
        >
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path d="M4 18h12a1 1 0 110 2H4a1 1 0 110-2zM6 2a1 1 0 011 1v6a3 3 0 106 0V3a1 1 0 112 0v6a5 5 0 11-10 0V3a1 1 0 011-1z" />
          </svg>
        </button>
        
        <button
          onClick={() => editor.chain().focus().toggleStrike().run()}
          className={`p-2 rounded hover:bg-gray-100 transition-colors ${
            editor.isActive('strike') ? 'bg-blue-100 text-blue-700' : 'text-gray-700'
          }`}
          title="Barré"
        >
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path d="M3 10h14a1 1 0 110 2H3a1 1 0 110-2zM6 4a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 00-1 1v2a1 1 0 11-2 0V6a3 3 0 013-3zM6 14a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 00-1 1v2a1 1 0 11-2 0v-2a3 3 0 013-3z" />
          </svg>
        </button>
      </div>

      {/* Exposant et indice */}
      <div className="flex items-center space-x-1 border-l border-gray-200 pl-2 ml-2">
        <button
          onClick={() => editor.chain().focus().toggleSuperscript().run()}
          className={`p-2 rounded hover:bg-gray-100 transition-colors text-xs ${
            editor.isActive('superscript') ? 'bg-blue-100 text-blue-700' : 'text-gray-700'
          }`}
          title="Exposant"
        >
          X²
        </button>
        
        <button
          onClick={() => editor.chain().focus().toggleSubscript().run()}
          className={`p-2 rounded hover:bg-gray-100 transition-colors text-xs ${
            editor.isActive('subscript') ? 'bg-blue-100 text-blue-700' : 'text-gray-700'
          }`}
          title="Indice"
        >
          X₂
        </button>
      </div>

      {/* Surlignage */}
      <div className="flex items-center space-x-1 border-l border-gray-200 pl-2 ml-2">
        <button
          onClick={() => editor.chain().focus().toggleHighlight().run()}
          className={`p-2 rounded hover:bg-gray-100 transition-colors ${
            editor.isActive('highlight') ? 'bg-yellow-100 text-yellow-700' : 'text-gray-700'
          }`}
          title="Surligner"
        >
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path d="M17.414 2.586a2 2 0 00-2.828 0L7 10.172V13h2.828l7.586-7.586a2 2 0 000-2.828z" />
            <path fillRule="evenodd" d="M2 6a2 2 0 012-2h4a1 1 0 010 2H4v10h10v-4a1 1 0 112 0v4a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" clipRule="evenodd" />
          </svg>
        </button>
      </div>

      {/* Effacer le formatage */}
      <div className="flex items-center space-x-1 border-l border-gray-200 pl-2 ml-2">
        <button
          onClick={() => editor.chain().focus().unsetAllMarks().run()}
          className="p-2 rounded hover:bg-gray-100 transition-colors text-gray-700"
          title="Effacer le formatage"
        >
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
          </svg>
        </button>
      </div>
    </div>
  )
}

'use client'

import React, { useState } from 'react'
import { NodeViewWrapper } from '@tiptap/react'

interface CoverPageNodeViewProps {
  node: any
  updateAttributes: (attributes: Record<string, any>) => void
  deleteNode: () => void
}

export default function CoverPageNodeView({
  node,
  updateAttributes,
  deleteNode
}: CoverPageNodeViewProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [editData, setEditData] = useState({
    title: node.attrs.title || '',
    subtitle: node.attrs.subtitle || '',
    documentType: node.attrs.documentType || '',
    documentIndice: node.attrs.documentIndice || '',
    date: node.attrs.date || '',
    author: node.attrs.author || '',
    company: node.attrs.company || '',
  })

  const handleSave = () => {
    updateAttributes(editData)
    setIsEditing(false)
  }

  const handleCancel = () => {
    setEditData({
      title: node.attrs.title || '',
      subtitle: node.attrs.subtitle || '',
      documentType: node.attrs.documentType || '',
      documentIndice: node.attrs.documentIndice || '',
      date: node.attrs.date || '',
      author: node.attrs.author || '',
      company: node.attrs.company || '',
    })
    setIsEditing(false)
  }

  if (isEditing) {
    return (
      <NodeViewWrapper className="cover-page-wrapper">
        <div className="cover-page-editor bg-white border-2 border-blue-500 rounded-lg p-6 space-y-4">
          <h3 className="text-lg font-semibold mb-4">Modifier la page de garde</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Titre principal
              </label>
              <input
                type="text"
                value={editData.title}
                onChange={(e) => setEditData({ ...editData, title: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Sous-titre
              </label>
              <input
                type="text"
                value={editData.subtitle}
                onChange={(e) => setEditData({ ...editData, subtitle: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Type de document
              </label>
              <input
                type="text"
                value={editData.documentType}
                onChange={(e) => setEditData({ ...editData, documentType: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Indice
              </label>
              <input
                type="text"
                value={editData.documentIndice}
                onChange={(e) => setEditData({ ...editData, documentIndice: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Date
              </label>
              <input
                type="text"
                value={editData.date}
                onChange={(e) => setEditData({ ...editData, date: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Auteur
              </label>
              <input
                type="text"
                value={editData.author}
                onChange={(e) => setEditData({ ...editData, author: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Entreprise
              </label>
              <input
                type="text"
                value={editData.company}
                onChange={(e) => setEditData({ ...editData, company: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
          
          <div className="flex justify-end space-x-3 pt-4">
            <button
              onClick={handleCancel}
              className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
            >
              Annuler
            </button>
            <button
              onClick={handleSave}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Sauvegarder
            </button>
          </div>
        </div>
      </NodeViewWrapper>
    )
  }

  // Rendu selon le template
  const renderCoverPageContent = () => {
    const template = node.attrs.template || 'default'

    switch (template) {
      case 'cctp':
        return renderCCTPTemplate()
      case 'dpgf':
        return renderDPGFTemplate()
      case 'rapport':
        return renderRapportTemplate()
      case 'custom':
        return renderCustomTemplate()
      default:
        return renderDefaultTemplate()
    }
  }

  const renderDefaultTemplate = () => (
    <div className="cover-page-content">
      <div className="cover-page-header">
        {node.attrs.logo && (
          <img src={node.attrs.logo} alt="Logo" className="cover-page-logo" />
        )}
      </div>

      <div className="cover-page-main">
        {node.attrs.title && (
          <h1 className="cover-page-title">{node.attrs.title}</h1>
        )}
        {node.attrs.subtitle && (
          <h2 className="cover-page-subtitle">{node.attrs.subtitle}</h2>
        )}

        <div className="cover-page-meta">
          {node.attrs.documentType && (
            <p className="document-type">
              {node.attrs.documentType}
              {node.attrs.documentIndice && ` - Indice ${node.attrs.documentIndice}`}
            </p>
          )}
          {node.attrs.date && <p className="document-date">{node.attrs.date}</p>}
        </div>
      </div>

      <div className="cover-page-footer">
        {node.attrs.author && <p className="document-author">Rédigé par : {node.attrs.author}</p>}
        {node.attrs.company && <p className="document-company">{node.attrs.company}</p>}
      </div>
    </div>
  )

  const renderCCTPTemplate = () => (
    <div className="cover-page-content cctp-template">
      <div className="cover-page-header">
        {node.attrs.logo && (
          <img src={node.attrs.logo} alt="Logo" className="cover-page-logo" />
        )}
        <div className="document-classification">
          <p className="classification-label">CAHIER DES CLAUSES TECHNIQUES PARTICULIÈRES</p>
          <p className="classification-type">CCTP</p>
        </div>
      </div>

      <div className="cover-page-main">
        {node.attrs.title && <h1 className="cover-page-title">{node.attrs.title}</h1>}
        {node.attrs.subtitle && <h2 className="cover-page-subtitle">{node.attrs.subtitle}</h2>}

        <div className="cover-page-meta cctp-meta">
          <div className="meta-row">
            <span className="meta-label">Document :</span>
            <span className="meta-value">{node.attrs.documentType}</span>
          </div>
          {node.attrs.documentIndice && (
            <div className="meta-row">
              <span className="meta-label">Indice :</span>
              <span className="meta-value">{node.attrs.documentIndice}</span>
            </div>
          )}
          {node.attrs.date && (
            <div className="meta-row">
              <span className="meta-label">Date :</span>
              <span className="meta-value">{node.attrs.date}</span>
            </div>
          )}
        </div>
      </div>

      <div className="cover-page-footer">
        {node.attrs.author && <p className="document-author">Rédigé par : {node.attrs.author}</p>}
        {node.attrs.company && <p className="document-company">{node.attrs.company}</p>}
      </div>
    </div>
  )

  const renderDPGFTemplate = () => (
    <div className="cover-page-content dpgf-template">
      <div className="cover-page-header">
        {node.attrs.logo && (
          <img src={node.attrs.logo} alt="Logo" className="cover-page-logo" />
        )}
        <div className="document-classification">
          <p className="classification-label">DÉTAIL DES PRIX GLOBAL ET FORFAITAIRE</p>
          <p className="classification-type">DPGF</p>
        </div>
      </div>

      <div className="cover-page-main">
        {node.attrs.title && <h1 className="cover-page-title">{node.attrs.title}</h1>}
        {node.attrs.subtitle && <h2 className="cover-page-subtitle">{node.attrs.subtitle}</h2>}

        <div className="cover-page-meta dpgf-meta">
          <table className="meta-table">
            <tbody>
              <tr>
                <td className="meta-label">Document :</td>
                <td className="meta-value">{node.attrs.documentType}</td>
              </tr>
              {node.attrs.documentIndice && (
                <tr>
                  <td className="meta-label">Indice :</td>
                  <td className="meta-value">{node.attrs.documentIndice}</td>
                </tr>
              )}
              {node.attrs.date && (
                <tr>
                  <td className="meta-label">Date :</td>
                  <td className="meta-value">{node.attrs.date}</td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      <div className="cover-page-footer">
        {node.attrs.author && <p className="document-author">Rédigé par : {node.attrs.author}</p>}
        {node.attrs.company && <p className="document-company">{node.attrs.company}</p>}
      </div>
    </div>
  )

  const renderRapportTemplate = () => (
    <div className="cover-page-content rapport-template">
      <div className="cover-page-header">
        {node.attrs.logo && (
          <img src={node.attrs.logo} alt="Logo" className="cover-page-logo" />
        )}
      </div>

      <div className="cover-page-main">
        <div className="document-type-badge">
          <span>{node.attrs.documentType}</span>
        </div>

        {node.attrs.title && <h1 className="cover-page-title">{node.attrs.title}</h1>}
        {node.attrs.subtitle && <h2 className="cover-page-subtitle">{node.attrs.subtitle}</h2>}

        <div className="cover-page-meta rapport-meta">
          {node.attrs.documentIndice && <p className="document-indice">Version {node.attrs.documentIndice}</p>}
          {node.attrs.date && <p className="document-date">{node.attrs.date}</p>}
        </div>
      </div>

      <div className="cover-page-footer">
        {node.attrs.author && <p className="document-author">{node.attrs.author}</p>}
        {node.attrs.company && <p className="document-company">{node.attrs.company}</p>}
      </div>
    </div>
  )

  const renderCustomTemplate = () => (
    <div className="cover-page-content custom-template">
      {node.attrs.customFields && Object.entries(node.attrs.customFields).map(([key, value]) => (
        <div key={key} className="custom-field" data-field={key}>
          {value as string}
        </div>
      ))}

      <div className="cover-page-main">
        {node.attrs.title && <h1 className="cover-page-title">{node.attrs.title}</h1>}
        {node.attrs.subtitle && <h2 className="cover-page-subtitle">{node.attrs.subtitle}</h2>}
      </div>
    </div>
  )

  return (
    <NodeViewWrapper className="cover-page-wrapper">
      <div className="cover-page-display bg-white border border-gray-200 rounded-lg relative group">
        {/* Boutons d'action */}
        <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity flex space-x-2 z-10">
          <button
            onClick={() => setIsEditing(true)}
            className="bg-blue-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm hover:bg-blue-600"
            title="Modifier"
          >
            ✏️
          </button>
          <button
            onClick={deleteNode}
            className="bg-red-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm hover:bg-red-600"
            title="Supprimer"
          >
            🗑️
          </button>
        </div>

        {/* Contenu de la page de garde selon le template */}
        {renderCoverPageContent()}
      </div>
    </NodeViewWrapper>
  )
}

import { OrbisDocContent, OrbisDocConfig } from '../types'

/**
 * Exporte un document au format Word (HTML compatible)
 */
export async function exportToWord(
  content: OrbisDocContent,
  config: OrbisDocConfig
): Promise<void> {
  try {
    const wordContent = generateWordHTML(content, config)
    const blob = new Blob([wordContent], { 
      type: 'application/msword;charset=utf-8' 
    })
    
    downloadFile(blob, `${config.documentTitle}.doc`)
    
  } catch (error) {
    console.error('Erreur lors de l\'export Word:', error)
    throw new Error('Impossible d\'exporter le document en Word')
  }
}

/**
 * Génère le HTML compatible Word
 */
function generateWordHTML(content: OrbisDocContent, config: OrbisDocConfig): string {
  const styles = generateWordStyles(config)
  const cleanContent = prepareContentForWord(content.html)
  
  return `
    <!DOCTYPE html>
    <html xmlns:o="urn:schemas-microsoft-com:office:office"
          xmlns:w="urn:schemas-microsoft-com:office:word"
          xmlns="http://www.w3.org/TR/REC-html40">
      <head>
        <meta charset="utf-8">
        <meta name="ProgId" content="Word.Document">
        <meta name="Generator" content="Microsoft Word">
        <meta name="Originator" content="Microsoft Word">
        <title>${config.documentTitle}</title>
        ${styles}
        <!--[if gte mso 9]>
        <xml>
          <w:WordDocument>
            <w:View>Print</w:View>
            <w:Zoom>90</w:Zoom>
            <w:DoNotPromptForConvert/>
            <w:DoNotShowRevisions/>
            <w:DoNotPrintRevisions/>
            <w:DoNotShowMarkup/>
            <w:DoNotShowComments/>
            <w:DoNotShowInsertionsAndDeletions/>
            <w:DoNotShowPropertyChanges/>
          </w:WordDocument>
        </xml>
        <![endif]-->
      </head>
      <body>
        <div class="WordSection1">
          ${cleanContent}
        </div>
      </body>
    </html>
  `
}

/**
 * Génère les styles CSS compatibles Word
 */
function generateWordStyles(config: OrbisDocConfig): string {
  return `
    <style>
      @page {
        size: ${config.pageSize || 'A4'};
        margin: ${config.margins?.top || 25}mm ${config.margins?.right || 20}mm 
                ${config.margins?.bottom || 25}mm ${config.margins?.left || 20}mm;
      }
      
      body {
        font-family: 'Times New Roman', serif;
        font-size: 12pt;
        line-height: 1.5;
        margin: 0;
        padding: 0;
        color: black;
        background: white;
      }
      
      .WordSection1 {
        page: WordSection1;
      }
      
      /* Styles des titres */
      h1 {
        font-size: 18pt;
        font-weight: bold;
        margin-top: 24pt;
        margin-bottom: 12pt;
        page-break-after: avoid;
      }
      
      h2 {
        font-size: 16pt;
        font-weight: bold;
        margin-top: 18pt;
        margin-bottom: 6pt;
        page-break-after: avoid;
      }
      
      h3 {
        font-size: 14pt;
        font-weight: bold;
        margin-top: 12pt;
        margin-bottom: 6pt;
        page-break-after: avoid;
      }
      
      h4 {
        font-size: 12pt;
        font-weight: bold;
        margin-top: 12pt;
        margin-bottom: 3pt;
        page-break-after: avoid;
      }
      
      h5 {
        font-size: 11pt;
        font-weight: bold;
        margin-top: 6pt;
        margin-bottom: 3pt;
        page-break-after: avoid;
      }
      
      h6 {
        font-size: 10pt;
        font-weight: bold;
        margin-top: 6pt;
        margin-bottom: 3pt;
        page-break-after: avoid;
      }
      
      /* Paragraphes */
      p {
        margin-top: 0pt;
        margin-bottom: 12pt;
        text-align: justify;
      }
      
      /* Listes */
      ul, ol {
        margin-top: 0pt;
        margin-bottom: 12pt;
        padding-left: 36pt;
      }
      
      li {
        margin-bottom: 6pt;
      }
      
      /* Tableaux */
      table {
        border-collapse: collapse;
        width: 100%;
        margin-bottom: 12pt;
      }
      
      th, td {
        border: 1pt solid black;
        padding: 6pt;
        vertical-align: top;
      }
      
      th {
        background-color: #f0f0f0;
        font-weight: bold;
        text-align: center;
      }
      
      /* Formatage du texte */
      strong, b {
        font-weight: bold;
      }
      
      em, i {
        font-style: italic;
      }
      
      u {
        text-decoration: underline;
      }
      
      /* Images */
      img {
        max-width: 100%;
        height: auto;
      }
      
      /* Sauts de page */
      .page-break {
        page-break-before: always;
      }
      
      /* Page de garde */
      .cover-page {
        text-align: center;
        page-break-after: always;
      }
      
      .cover-page-title {
        font-size: 24pt;
        font-weight: bold;
        margin-bottom: 24pt;
      }
      
      .cover-page-subtitle {
        font-size: 18pt;
        font-style: italic;
        margin-bottom: 36pt;
      }
      
      /* Styles spécifiques aux templates */
      .cctp-template .classification-label {
        font-size: 10pt;
        font-weight: bold;
        text-transform: uppercase;
      }
      
      .cctp-template .classification-type {
        font-size: 20pt;
        font-weight: bold;
        margin: 12pt 0;
      }
      
      .dpgf-template .meta-table {
        margin: 24pt auto;
        max-width: 500px;
      }
      
      .rapport-template .document-type-badge {
        display: inline-block;
        background: black;
        color: white;
        padding: 6pt 12pt;
        margin-bottom: 12pt;
        font-weight: bold;
      }
    </style>
  `
}

/**
 * Prépare le contenu HTML pour Word
 */
function prepareContentForWord(html: string): string {
  let cleanHtml = html
  
  // Supprimer les éléments non compatibles
  cleanHtml = cleanHtml
    .replace(/<button[^>]*>.*?<\/button>/gi, '')
    .replace(/<div[^>]*class="[^"]*no-print[^"]*"[^>]*>.*?<\/div>/gi, '')
    .replace(/<span[^>]*class="[^"]*hover-only[^"]*"[^>]*>.*?<\/span>/gi, '')
  
  // Convertir les sauts de page
  cleanHtml = cleanHtml.replace(
    /<div[^>]*data-type="page-break"[^>]*>.*?<\/div>/gi,
    '<div class="page-break"></div>'
  )
  
  // Nettoyer les attributs non supportés par Word
  cleanHtml = cleanHtml
    .replace(/contenteditable="[^"]*"/gi, '')
    .replace(/spellcheck="[^"]*"/gi, '')
    .replace(/data-[^=]*="[^"]*"/gi, '')
  
  return cleanHtml
}

/**
 * Télécharge un fichier
 */
function downloadFile(blob: Blob, filename: string): void {
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = filename
  link.style.display = 'none'
  
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  
  // Nettoyer l'URL
  setTimeout(() => URL.revokeObjectURL(url), 100)
}

/**
 * Exporte au format DOCX (nécessite une bibliothèque externe)
 * Cette fonction est un placeholder pour une future implémentation
 */
export async function exportToDOCX(
  content: OrbisDocContent,
  config: OrbisDocConfig
): Promise<void> {
  // Pour une vraie implémentation DOCX, il faudrait utiliser une bibliothèque
  // comme docx ou mammoth.js
  console.warn('Export DOCX non implémenté, utilisation du format DOC à la place')
  return exportToWord(content, config)
}

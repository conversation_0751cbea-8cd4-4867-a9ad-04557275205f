import { useEffect, useRef } from 'react'
import { Editor } from '@tiptap/react'
import { OrbisDocConfig } from '../types'

interface UseAutoSaveProps {
  editor: Editor | null
  config: OrbisDocConfig
  onSave: () => Promise<void>
  hasUnsavedChanges: boolean
}

export function useAutoSave({
  editor,
  config,
  onSave,
  hasUnsavedChanges
}: UseAutoSaveProps) {
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)
  const lastSaveRef = useRef<string>('')

  useEffect(() => {
    // Si l'auto-sauvegarde n'est pas activée, ne rien faire
    if (!config.autoSave || !editor || !hasUnsavedChanges) {
      return
    }

    // Nettoyer le timeout précédent
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }

    // Obtenir le contenu actuel
    const currentContent = editor.getHTML()

    // Si le contenu n'a pas changé depuis la dernière sauvegarde, ne pas sauvegarder
    if (currentContent === lastSaveRef.current) {
      return
    }

    // Programmer la sauvegarde automatique
    timeoutRef.current = setTimeout(async () => {
      try {
        await onSave()
        lastSaveRef.current = currentContent
        console.log('Auto-sauvegarde effectuée')
      } catch (error) {
        console.error('Erreur lors de l\'auto-sauvegarde:', error)
      }
    }, config.autoSaveInterval || 30000) // 30 secondes par défaut

    // Nettoyer le timeout au démontage
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [editor, config.autoSave, config.autoSaveInterval, hasUnsavedChanges, onSave])

  // Nettoyer au démontage du composant
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [])
}

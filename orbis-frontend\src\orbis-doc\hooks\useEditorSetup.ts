import { useEditor } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import TextAlign from '@tiptap/extension-text-align'
import Underline from '@tiptap/extension-underline'
import Subscript from '@tiptap/extension-subscript'
import Superscript from '@tiptap/extension-superscript'
import Highlight from '@tiptap/extension-highlight'
import Table from '@tiptap/extension-table'
import TableRow from '@tiptap/extension-table-row'
import TableHeader from '@tiptap/extension-table-header'
import TableCell from '@tiptap/extension-table-cell'
import Image from '@tiptap/extension-image'
import Link from '@tiptap/extension-link'
import Typography from '@tiptap/extension-typography'
import Placeholder from '@tiptap/extension-placeholder'

// Extensions personnalisées
import { PageBreakExtension } from '../extensions/PageBreakExtension'
import { CoverPageExtension } from '../extensions/CoverPageExtension'
import { NumberedHeadingExtension } from '../extensions/NumberedHeadingExtension'
import { FooterExtension } from '../extensions/FooterExtension'

import { OrbisDocConfig } from '../types'

interface UseEditorSetupProps {
  config: OrbisDocConfig
  initialContent?: string
  onChange?: (content: string) => void
}

export function useEditorSetup({
  config,
  initialContent = '',
  onChange
}: UseEditorSetupProps) {
  
  const editor = useEditor({
    extensions: [
      // Extensions de base de TipTap
      StarterKit.configure({
        heading: false, // On utilise notre extension personnalisée
        paragraph: {
          HTMLAttributes: {
            class: 'orbis-paragraph',
          },
        },
        bulletList: {
          HTMLAttributes: {
            class: 'orbis-bullet-list',
          },
        },
        orderedList: {
          HTMLAttributes: {
            class: 'orbis-ordered-list',
          },
        },
      }),

      // Extensions de formatage
      TextAlign.configure({
        types: ['heading', 'paragraph'],
        alignments: ['left', 'center', 'right', 'justify'],
        defaultAlignment: 'left',
      }),
      
      Underline,
      Subscript,
      Superscript,
      
      Highlight.configure({
        multicolor: true,
      }),

      // Extensions de contenu
      Image.configure({
        HTMLAttributes: {
          class: 'orbis-image',
        },
        allowBase64: true,
      }),

      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: 'orbis-link',
        },
      }),

      // Tables
      Table.configure({
        HTMLAttributes: {
          class: 'orbis-table',
        },
      }),
      TableRow,
      TableHeader,
      TableCell,

      // Améliorations typographiques
      Typography,

      // Placeholder
      Placeholder.configure({
        placeholder: ({ node }) => {
          if (node.type.name === 'heading') {
            return 'Titre...'
          }
          return 'Commencez à écrire votre document...'
        },
        includeChildren: true,
      }),

      // Extensions personnalisées orbis-doc
      NumberedHeadingExtension.configure({
        levels: [1, 2, 3, 4, 5, 6],
        HTMLAttributes: {
          class: 'orbis-heading',
        },
      }),

      PageBreakExtension.configure({
        HTMLAttributes: {
          class: 'orbis-page-break',
        },
      }),

      CoverPageExtension.configure({
        HTMLAttributes: {
          class: 'orbis-cover-page',
        },
      }),

      FooterExtension.configure({
        documentTitle: config.documentTitle,
        showPageNumbers: true,
      }),
    ],

    content: initialContent,
    
    editable: !config.readOnly,
    
    onUpdate: ({ editor }) => {
      const html = editor.getHTML()
      if (onChange) {
        onChange(html)
      }
    },

    editorProps: {
      attributes: {
        class: 'orbis-doc-content prose prose-sm sm:prose lg:prose-lg xl:prose-2xl mx-auto focus:outline-none',
        spellcheck: 'true',
      },
    },

    // Configuration pour l'impression
    parseOptions: {
      preserveWhitespace: 'full',
    },
  })

  return {
    editor
  }
}

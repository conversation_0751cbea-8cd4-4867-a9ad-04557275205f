'use client'

import React from 'react'
import { OrbisDocConfig, OrbisDocContent } from '../types'
import DocumentPages from './DocumentPages'
import '../styles/orbis-doc.css'

interface OrbisDocViewerProps {
  config: OrbisDocConfig
  content: OrbisDocContent
  className?: string
}

export default function OrbisDocViewer({
  config,
  content,
  className = ''
}: OrbisDocViewerProps) {
  
  // Configuration en mode lecture seule
  const viewerConfig: OrbisDocConfig = {
    ...config,
    mode: 'view',
    readOnly: true,
  }

  return (
    <div className={`orbis-doc-viewer ${className}`}>
      {/* En-tête du document */}
      <div className="viewer-header bg-white border-b border-gray-200 p-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-xl font-semibold text-gray-900">
              {config.documentTitle}
            </h1>
            <p className="text-sm text-gray-600">
              {config.documentType}
              {config.documentIndice && ` - Indice ${config.documentIndice}`}
            </p>
          </div>
          
          <div className="flex items-center space-x-4 text-sm text-gray-600">
            <span>{content.totalPages} page{content.totalPages > 1 ? 's' : ''}</span>
            <span>{content.wordCount} mot{content.wordCount > 1 ? 's' : ''}</span>
          </div>
        </div>
      </div>

      {/* Contenu du document */}
      <div className="viewer-content bg-gray-50 p-6">
        <div 
          className="document-content"
          dangerouslySetInnerHTML={{ __html: content.html }}
        />
      </div>
    </div>
  )
}

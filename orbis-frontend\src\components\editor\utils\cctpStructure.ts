/**
 * Utilitaires pour la structure des documents CCTP
 */

export const initializeCCTPWithCoverPage = () => {
  return `
    <h1>1. Objet du marché</h1>
    <p>Description générale des travaux et de leur objet.</p>
    <p>Le présent marché a pour objet...</p>

    <h1>2. Dispositions générales</h1>
    
    <h2>2.1 Réglementation</h2>
    <p>Les travaux seront exécutés conformément aux textes réglementaires en vigueur :</p>
    <ul>
      <li>Code de la construction et de l'habitation</li>
      <li>Règlement de sécurité contre l'incendie</li>
      <li>Réglementation thermique en vigueur</li>
      <li>Réglementation accessibilité PMR</li>
    </ul>

    <h2>2.2 Normes</h2>
    <p>Les travaux respecteront les normes techniques en vigueur :</p>
    <ul>
      <li>Normes NF et européennes applicables</li>
      <li>DTU (Documents Techniques Unifiés)</li>
      <li>Avis techniques du CSTB</li>
    </ul>

    <h2>2.3 Documents de référence</h2>
    <p>Les documents contractuels comprennent :</p>
    <ul>
      <li>Le présent CCTP</li>
      <li>Les plans d'exécution</li>
      <li>Le DPGF</li>
      <li>Les spécifications techniques particulières</li>
    </ul>

    <h1>3. Description des ouvrages</h1>
    
    <h2>3.1 Gros œuvre</h2>
    <p>Spécifications techniques pour les travaux de gros œuvre...</p>
    
    <h3>3.1.1 Fondations</h3>
    <p>Description des travaux de fondations...</p>
    
    <h3>3.1.2 Structure</h3>
    <p>Description de la structure porteuse...</p>

    <h2>3.2 Second œuvre</h2>
    <p>Spécifications techniques pour les travaux de second œuvre...</p>
    
    <h3>3.2.1 Cloisons</h3>
    <p>Description des cloisons...</p>
    
    <h3>3.2.2 Revêtements</h3>
    <p>Description des revêtements...</p>

    <h1>4. Modalités d'exécution</h1>
    
    <h2>4.1 Préparation du chantier</h2>
    <p>Modalités de préparation et d'installation de chantier...</p>
    
    <h2>4.2 Coordination</h2>
    <p>Modalités de coordination entre les différents corps d'état...</p>
    
    <h2>4.3 Contrôles et réception</h2>
    <p>Modalités de contrôle et de réception des travaux...</p>

    <h1>5. Garanties et maintenance</h1>
    
    <h2>5.1 Garanties</h2>
    <p>Garanties applicables aux travaux :</p>
    <ul>
      <li>Garantie de parfait achèvement (1 an)</li>
      <li>Garantie de bon fonctionnement (2 ans)</li>
      <li>Garantie décennale (10 ans)</li>
    </ul>
    
    <h2>5.2 Maintenance</h2>
    <p>Modalités de maintenance et d'entretien...</p>
  `
}

export const initializeDPGFWithCoverPage = () => {
  return `
    <h1>1. Objet de la décomposition</h1>
    <p>La présente décomposition du prix global et forfaitaire détaille...</p>

    <h1>2. Conditions générales</h1>
    
    <h2>2.1 Base de prix</h2>
    <p>Les prix sont établis sur la base de...</p>
    
    <h2>2.2 Révision des prix</h2>
    <p>Les modalités de révision des prix...</p>

    <h1>3. Décomposition par lots</h1>
    
    <h2>3.1 Lot Gros œuvre</h2>
    <p>Décomposition détaillée du lot gros œuvre...</p>
    
    <h2>3.2 Lot Second œuvre</h2>
    <p>Décomposition détaillée du lot second œuvre...</p>

    <h1>4. Récapitulatif des prix</h1>
    <p>Récapitulatif général des prix par lot...</p>
  `
}

export const getDocumentStructure = (documentType: string) => {
  switch (documentType.toUpperCase()) {
    case 'CCTP':
      return initializeCCTPWithCoverPage()
    case 'DPGF':
      return initializeDPGFWithCoverPage()
    default:
      return `
        <h1>1. Introduction</h1>
        <p>Contenu du document...</p>
        
        <h1>2. Développement</h1>
        <p>Développement du contenu...</p>
        
        <h1>3. Conclusion</h1>
        <p>Conclusion du document...</p>
      `
  }
}

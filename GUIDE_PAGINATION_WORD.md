# 📄 Guide de la Pagination Type Word

## 🎯 Vue d'ensemble

Cette fonctionnalité ajoute une pagination similaire à Microsoft Word dans l'éditeur TipTap pour les documents CCTP/DPGF. Elle simule l'apparence et le comportement des pages physiques avec des pieds de page automatiques et une numérotation.

## ✨ Fonctionnalités principales

### 📋 Pages visuelles
- **Format A4** : Dimensions exactes (794px × 1123px à 96 DPI)
- **Marges réalistes** : 30px de marge sur tous les côtés
- **Ombres portées** : Effet visuel pour simuler des pages physiques
- **Arrière-plan** : Fond gris clair pour distinguer les pages

### 📄 Gestion spéciale de la page de garde
- **Page de garde** : Ne compte pas dans la numérotation des pages
- **Page vierge automatique** : Insérée automatiquement après la page de garde
- **Structure CCTP** : Page de garde + page vierge + contenu (Page 1, 2, 3...)
- **Bordure spéciale** : Page de garde avec bordure noire de 3px

### 🦶 Pieds de page automatiques
- **Titre du document** : Affiché à gauche du pied de page
- **Numérotation** : "Page X / Y" affiché à droite (commence à 1 après la page vierge)
- **Séparateur visuel** : Ligne horizontale au-dessus du pied de page
- **Style cohérent** : Police 10pt, couleur grise
- **Exclusions** : Pas de pied de page sur la page de garde ni la page vierge

### 🔄 Pagination automatique
- **Calcul dynamique** : Le nombre de pages s'ajuste automatiquement au contenu
- **Débordement intelligent** : Le contenu qui dépasse crée une nouvelle page
- **Mise à jour en temps réel** : Recalcul lors des modifications de contenu

### ✂️ Sauts de page manuels
- **Bouton dédié** : Icône 📄 dans la barre d'outils
- **Raccourci clavier** : `Ctrl+Entrée` pour insérer un saut de page
- **Indicateur visuel** : Ligne pointillée avec texte "--- Saut de page ---"
- **Impression optimisée** : Les indicateurs sont masqués à l'impression

### 🖨️ Impression optimisée
- **Styles CSS spéciaux** : Configuration `@media print` dédiée
- **Format A4 exact** : 210mm × 297mm pour l'impression
- **Marges d'impression** : 20mm sur tous les côtés
- **Police d'impression** : Times New Roman 12pt pour la compatibilité
- **Masquage des éléments** : Barres d'outils et indicateurs cachés

## 🚀 Utilisation

### Basculer entre les modes
1. Ouvrez un document CCTP/DPGF
2. Utilisez les boutons en haut à droite :
   - **📝 Éditeur** : Mode normal (vue continue)
   - **📄 Pages** : Mode paginé (vue pages séparées)

### Créer un document CCTP complet
1. Cliquez sur **"Générer page de garde"** dans la sidebar
2. Le système crée automatiquement :
   - **Page de garde** avec informations du projet
   - **Page vierge** (page intentionnellement laissée vierge)
   - **Contenu structuré** du CCTP (Page 1, 2, 3...)

### Insérer un saut de page
1. Placez le curseur où vous voulez le saut
2. Cliquez sur le bouton **📄 Saut de page** dans la barre d'outils
3. Ou utilisez le raccourci `Ctrl+Entrée`

### Navigation entre pages
- **Boutons de navigation** : ← Page précédente / Page suivante →
- **Indicateur de position** : "Page X / Y" affiché en permanence (commence après la page vierge)
- **Défilement fluide** : Animation lors du changement de page

## 🛠️ Architecture technique

### Composants créés
```
📁 components/editor/
├── 📄 extensions/PageBreak.ts          # Extension TipTap pour sauts de page
├── 📄 hooks/usePagination.ts           # Hook pour gestion pagination
├── 📄 components/
│   ├── PaginatedEditor.tsx             # Éditeur avec pagination complète
│   └── PaginationToolbar.tsx           # Barre d'outils pagination
├── 📄 PaginatedDocumentEditor.tsx      # Wrapper principal
└── 📄 styles/pagination.css            # Styles CSS pagination
```

### Extensions TipTap
- **PageBreak** : Node personnalisé pour les sauts de page manuels
- **Intégration** : Ajouté aux extensions existantes (AutoNumberedHeading, CoverPageNode)

### Hooks personnalisés
- **usePagination** : Gère le calcul automatique des pages et la navigation
- **Paramètres configurables** : Hauteur de page, hauteur pied de page, titre document

## 🎨 Personnalisation

### Taille des pages
```css
/* Modifier dans pagination.css */
.document-page {
  width: 794px;  /* Largeur A4 à 96 DPI */
  min-height: 1123px; /* Hauteur A4 à 96 DPI */
}
```

### Contenu du pied de page
```typescript
// Modifier dans usePagination.ts
const generateFooter = (pageNumber: number) => {
  return `
    <div class="page-footer">
      <div class="footer-content">
        <span class="document-title">${documentTitle}</span>
        <span class="page-number">Page ${pageNumber} / ${pages.length}</span>
      </div>
    </div>
  `
}
```

### Styles visuels
```css
/* Couleurs et polices dans pagination.css */
.page-footer {
  font-size: 10pt;
  color: #666;
  border-top: 1px solid #e5e7eb;
}

.document-page {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  background: white;
}
```

### Hauteur de page
```typescript
// Modifier dans usePagination.ts
const usePagination = ({
  pageHeight = 1123, // Hauteur en pixels
  footerHeight = 60  // Hauteur pied de page
}) => {
  // Logique de calcul...
}
```

## 📱 Responsive Design

### Adaptation mobile
- **Largeur flexible** : Pages s'adaptent aux petits écrans
- **Marges réduites** : Optimisation pour mobiles/tablettes
- **Barre d'outils** : Réorganisation des boutons sur petits écrans

### Points de rupture
```css
@media (max-width: 900px) {
  .document-page {
    width: 100%;
    padding: 20px;
  }
  
  .pagination-toolbar {
    flex-wrap: wrap;
    gap: 8px;
  }
}
```

## 🔧 Configuration avancée

### Formats de page personnalisés
Pour d'autres formats que A4, modifier les dimensions :
```css
/* Format Letter US */
.document-page {
  width: 816px;  /* 8.5" à 96 DPI */
  min-height: 1056px; /* 11" à 96 DPI */
}
```

### Marges personnalisées
```css
.page-content {
  padding: 40px; /* Marges intérieures */
}
```

### Styles d'impression personnalisés
```css
@media print {
  @page {
    size: A4;
    margin: 15mm; /* Marges d'impression */
  }
}
```

## 🐛 Dépannage

### Problèmes courants

1. **Pages ne s'affichent pas** : Vérifier que les styles CSS sont bien importés
2. **Pagination incorrecte** : Vérifier la hauteur du contenu et les calculs
3. **Sauts de page invisibles** : Vérifier l'extension PageBreak dans les extensions TipTap
4. **Impression décalée** : Vérifier les styles `@media print`

### Debug
```typescript
// Activer les logs dans usePagination.ts
console.log('Content height:', contentHeight)
console.log('Number of pages:', numberOfPages)
console.log('Usable page height:', usablePageHeight)
```

## 📈 Performances

### Optimisations
- **Debounce** : Calcul de pagination avec délai (300ms)
- **Mise à jour conditionnelle** : Recalcul uniquement si nécessaire
- **Cleanup** : Nettoyage des timeouts et event listeners

### Bonnes pratiques
- Éviter les recalculs fréquents
- Utiliser `useCallback` pour les fonctions coûteuses
- Optimiser les re-renders avec `useMemo`

## 🔮 Évolutions futures

### Fonctionnalités prévues
- **En-têtes de page** : Ajout d'en-têtes personnalisables
- **Numérotation avancée** : Numérotation par section
- **Marges variables** : Configuration dynamique des marges
- **Formats multiples** : Support A3, Letter, Legal, etc.
- **Aperçu impression** : Mode aperçu avant impression
- **Export PDF** : Génération PDF avec pagination

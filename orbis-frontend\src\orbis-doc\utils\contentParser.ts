import { OrbisDocContent } from '../types'

/**
 * Parse le contenu HTML pour extraire des informations
 */
export function parseDocumentContent(html: string): OrbisDocContent {
  const parser = new DOMParser()
  const doc = parser.parseFromString(html, 'text/html')
  
  return {
    html,
    json: null, // Sera rempli par TipTap
    pages: [],
    totalPages: 1,
    wordCount: countWords(html),
    characterCount: countCharacters(html)
  }
}

/**
 * Compte les mots dans le HTML
 */
export function countWords(html: string): number {
  // Supprimer les balises HTML
  const text = html.replace(/<[^>]*>/g, ' ')
  // Supprimer les espaces multiples et nettoyer
  const cleanText = text.replace(/\s+/g, ' ').trim()
  // Compter les mots
  return cleanText ? cleanText.split(' ').length : 0
}

/**
 * Compte les caractères dans le HTML (sans les balises)
 */
export function countCharacters(html: string): number {
  const text = html.replace(/<[^>]*>/g, '')
  return text.length
}

/**
 * Extrait les titres du document
 */
export function extractHeadings(html: string): Array<{
  level: number
  text: string
  id?: string
}> {
  const headings: Array<{ level: number; text: string; id?: string }> = []
  const parser = new DOMParser()
  const doc = parser.parseFromString(html, 'text/html')
  
  const headingElements = doc.querySelectorAll('h1, h2, h3, h4, h5, h6')
  headingElements.forEach((heading) => {
    const level = parseInt(heading.tagName.substring(1))
    const text = heading.textContent || ''
    const id = heading.id || undefined
    
    headings.push({ level, text, id })
  })
  
  return headings
}

/**
 * Extrait les images du document
 */
export function extractImages(html: string): Array<{
  src: string
  alt?: string
  title?: string
}> {
  const images: Array<{ src: string; alt?: string; title?: string }> = []
  const parser = new DOMParser()
  const doc = parser.parseFromString(html, 'text/html')
  
  const imageElements = doc.querySelectorAll('img')
  imageElements.forEach((img) => {
    images.push({
      src: img.src,
      alt: img.alt || undefined,
      title: img.title || undefined
    })
  })
  
  return images
}

/**
 * Extrait les tableaux du document
 */
export function extractTables(html: string): Array<{
  headers: string[]
  rows: string[][]
}> {
  const tables: Array<{ headers: string[]; rows: string[][] }> = []
  const parser = new DOMParser()
  const doc = parser.parseFromString(html, 'text/html')
  
  const tableElements = doc.querySelectorAll('table')
  tableElements.forEach((table) => {
    const headers: string[] = []
    const rows: string[][] = []
    
    // Extraire les en-têtes
    const headerCells = table.querySelectorAll('th')
    headerCells.forEach((th) => {
      headers.push(th.textContent || '')
    })
    
    // Extraire les lignes
    const rowElements = table.querySelectorAll('tbody tr, tr')
    rowElements.forEach((tr) => {
      const cells = tr.querySelectorAll('td')
      if (cells.length > 0) {
        const row: string[] = []
        cells.forEach((td) => {
          row.push(td.textContent || '')
        })
        rows.push(row)
      }
    })
    
    tables.push({ headers, rows })
  })
  
  return tables
}

/**
 * Extrait les liens du document
 */
export function extractLinks(html: string): Array<{
  href: string
  text: string
  title?: string
}> {
  const links: Array<{ href: string; text: string; title?: string }> = []
  const parser = new DOMParser()
  const doc = parser.parseFromString(html, 'text/html')
  
  const linkElements = doc.querySelectorAll('a[href]')
  linkElements.forEach((link) => {
    links.push({
      href: (link as HTMLAnchorElement).href,
      text: link.textContent || '',
      title: link.title || undefined
    })
  })
  
  return links
}

/**
 * Génère un résumé du document
 */
export function generateDocumentSummary(html: string): {
  wordCount: number
  characterCount: number
  headingCount: number
  imageCount: number
  tableCount: number
  linkCount: number
  estimatedReadingTime: number
} {
  const wordCount = countWords(html)
  const characterCount = countCharacters(html)
  const headings = extractHeadings(html)
  const images = extractImages(html)
  const tables = extractTables(html)
  const links = extractLinks(html)
  
  // Estimation du temps de lecture (250 mots par minute)
  const estimatedReadingTime = Math.ceil(wordCount / 250)
  
  return {
    wordCount,
    characterCount,
    headingCount: headings.length,
    imageCount: images.length,
    tableCount: tables.length,
    linkCount: links.length,
    estimatedReadingTime
  }
}

/**
 * Nettoie le HTML en supprimant les éléments indésirables
 */
export function cleanHTML(html: string): string {
  const parser = new DOMParser()
  const doc = parser.parseFromString(html, 'text/html')
  
  // Supprimer les scripts
  const scripts = doc.querySelectorAll('script')
  scripts.forEach(script => script.remove())
  
  // Supprimer les styles inline dangereux
  const elementsWithStyle = doc.querySelectorAll('[style]')
  elementsWithStyle.forEach(element => {
    const style = element.getAttribute('style') || ''
    if (style.includes('javascript:') || style.includes('expression(')) {
      element.removeAttribute('style')
    }
  })
  
  // Supprimer les attributs dangereux
  const dangerousAttributes = ['onload', 'onerror', 'onclick', 'onmouseover']
  dangerousAttributes.forEach(attr => {
    const elements = doc.querySelectorAll(`[${attr}]`)
    elements.forEach(element => element.removeAttribute(attr))
  })
  
  return doc.body.innerHTML
}

/**
 * Convertit le HTML en texte brut
 */
export function htmlToText(html: string): string {
  const parser = new DOMParser()
  const doc = parser.parseFromString(html, 'text/html')
  
  // Remplacer les sauts de ligne par des espaces
  const text = doc.body.textContent || ''
  return text.replace(/\s+/g, ' ').trim()
}

/**
 * Recherche du texte dans le HTML
 */
export function searchInDocument(html: string, searchTerm: string): Array<{
  context: string
  position: number
}> {
  const text = htmlToText(html)
  const results: Array<{ context: string; position: number }> = []
  const searchRegex = new RegExp(searchTerm, 'gi')
  
  let match
  while ((match = searchRegex.exec(text)) !== null) {
    const start = Math.max(0, match.index - 50)
    const end = Math.min(text.length, match.index + searchTerm.length + 50)
    const context = text.substring(start, end)
    
    results.push({
      context: context,
      position: match.index
    })
  }
  
  return results
}

/**
 * Extrait les métadonnées du document
 */
export function extractMetadata(html: string): {
  title?: string
  description?: string
  keywords?: string[]
  author?: string
  created?: Date
  modified?: Date
} {
  const parser = new DOMParser()
  const doc = parser.parseFromString(html, 'text/html')
  
  // Chercher le titre dans les balises meta ou h1
  let title = doc.querySelector('meta[name="title"]')?.getAttribute('content')
  if (!title) {
    const h1 = doc.querySelector('h1')
    title = h1?.textContent || undefined
  }
  
  const description = doc.querySelector('meta[name="description"]')?.getAttribute('content')
  const keywordsContent = doc.querySelector('meta[name="keywords"]')?.getAttribute('content')
  const keywords = keywordsContent ? keywordsContent.split(',').map(k => k.trim()) : undefined
  const author = doc.querySelector('meta[name="author"]')?.getAttribute('content')
  
  return {
    title,
    description,
    keywords,
    author
  }
}

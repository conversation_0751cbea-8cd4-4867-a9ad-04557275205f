'use client'

import React, { useState } from 'react'
import { OrbisDocConfig, OrbisDocCoverPage } from '../types'

interface CoverPageModalProps {
  isOpen: boolean
  onClose: () => void
  onGenerate: (coverPage: OrbisDocCoverPage) => void
  config: OrbisDocConfig
}

export default function CoverPageModal({
  isOpen,
  onClose,
  onGenerate,
  config
}: CoverPageModalProps) {
  
  const [formData, setFormData] = useState<OrbisDocCoverPage>({
    title: config.documentTitle || 'Titre du document',
    subtitle: '',
    documentType: config.documentType || 'Document',
    documentIndice: config.documentIndice || '01',
    date: new Date().toLocaleDateString('fr-FR'),
    author: '',
    company: '',
    logo: '',
    template: 'default',
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onGenerate(formData)
  }

  const handleChange = (field: keyof OrbisDocCoverPage, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-gray-900">
              Créer une page de garde
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Titre principal */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Titre principal *
              </label>
              <input
                type="text"
                required
                value={formData.title}
                onChange={(e) => handleChange('title', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Titre du document"
              />
            </div>

            {/* Sous-titre */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Sous-titre
              </label>
              <input
                type="text"
                value={formData.subtitle}
                onChange={(e) => handleChange('subtitle', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Sous-titre (optionnel)"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Type de document */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Type de document
                </label>
                <select
                  value={formData.documentType}
                  onChange={(e) => handleChange('documentType', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="CCTP">CCTP</option>
                  <option value="DPGF">DPGF</option>
                  <option value="RAPPORT">Rapport</option>
                  <option value="DEVIS">Devis</option>
                  <option value="AUTRE">Autre</option>
                </select>
              </div>

              {/* Indice */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Indice
                </label>
                <input
                  type="text"
                  value={formData.documentIndice}
                  onChange={(e) => handleChange('documentIndice', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="01"
                />
              </div>

              {/* Date */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Date
                </label>
                <input
                  type="text"
                  value={formData.date}
                  onChange={(e) => handleChange('date', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="JJ/MM/AAAA"
                />
              </div>

              {/* Auteur */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Auteur
                </label>
                <input
                  type="text"
                  value={formData.author}
                  onChange={(e) => handleChange('author', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Nom de l'auteur"
                />
              </div>
            </div>

            {/* Entreprise */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Entreprise
              </label>
              <input
                type="text"
                value={formData.company}
                onChange={(e) => handleChange('company', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Nom de l'entreprise"
              />
            </div>

            {/* Template */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Modèle de page de garde
              </label>
              <select
                value={formData.template}
                onChange={(e) => handleChange('template', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="default">Par défaut - Simple et épuré</option>
                <option value="cctp">CCTP - Spécialisé pour les CCTP</option>
                <option value="dpgf">DPGF - Spécialisé pour les DPGF</option>
                <option value="rapport">Rapport - Pour les rapports techniques</option>
                <option value="custom">Personnalisé - Entièrement personnalisable</option>
              </select>
              <p className="text-xs text-gray-500 mt-1">
                Choisissez le modèle qui correspond le mieux à votre type de document
              </p>
            </div>

            {/* Aperçu du template */}
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
              <h4 className="text-sm font-medium text-gray-700 mb-2">Aperçu du modèle</h4>
              <div className="text-xs text-gray-600">
                {formData.template === 'default' && (
                  <p>Modèle simple avec titre, sous-titre et informations de base centrées.</p>
                )}
                {formData.template === 'cctp' && (
                  <p>Modèle professionnel avec en-tête "CCTP", classification et tableau de métadonnées.</p>
                )}
                {formData.template === 'dpgf' && (
                  <p>Modèle spécialisé avec en-tête "DPGF" et présentation tabulaire des informations.</p>
                )}
                {formData.template === 'rapport' && (
                  <p>Modèle moderne avec badge de type de document et mise en page épurée.</p>
                )}
                {formData.template === 'custom' && (
                  <p>Modèle personnalisable permettant d'ajouter des champs spécifiques.</p>
                )}
              </div>
            </div>

            {/* Boutons d'action */}
            <div className="flex justify-end space-x-3 pt-6">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
              >
                Annuler
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                Créer la page de garde
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}

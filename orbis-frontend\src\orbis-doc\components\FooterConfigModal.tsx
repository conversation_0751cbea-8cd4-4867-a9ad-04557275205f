'use client'

import React, { useState } from 'react'
import { OrbisDocFooter, OrbisDocConfig } from '../types'
import { footerTemplates, generateFooterPreview, validateFooterConfig } from '../utils/footerGenerator'

interface FooterConfigModalProps {
  isOpen: boolean
  onClose: () => void
  onSave: (footer: OrbisDocFooter) => void
  currentFooter: OrbisDocFooter
  config: OrbisDocConfig
}

export default function FooterConfigModal({
  isOpen,
  onClose,
  onSave,
  currentFooter,
  config
}: FooterConfigModalProps) {
  
  const [footerData, setFooterData] = useState<OrbisDocFooter>(currentFooter)
  const [selectedTemplate, setSelectedTemplate] = useState<string>('custom')
  const [errors, setErrors] = useState<string[]>([])

  const handleTemplateChange = (templateKey: string) => {
    setSelectedTemplate(templateKey)
    
    if (templateKey !== 'custom') {
      const template = footerTemplates[templateKey as keyof typeof footerTemplates]
      if (template) {
        setFooterData(template.template)
      }
    }
  }

  const handleFieldChange = (field: keyof OrbisDocFooter, value: string | boolean) => {
    setFooterData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSave = () => {
    const validation = validateFooterConfig(footerData)
    
    if (!validation.isValid) {
      setErrors(validation.errors)
      return
    }
    
    setErrors([])
    onSave(footerData)
    onClose()
  }

  const previewHtml = generateFooterPreview(footerData, config)

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-gray-900">
              Configuration du pied de page
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Configuration */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Configuration</h3>
              
              {/* Sélection de template */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Template prédéfini
                </label>
                <select
                  value={selectedTemplate}
                  onChange={(e) => handleTemplateChange(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="custom">Personnalisé</option>
                  {Object.entries(footerTemplates).map(([key, template]) => (
                    <option key={key} value={key}>
                      {template.name} - {template.description}
                    </option>
                  ))}
                </select>
              </div>

              {/* Configuration personnalisée */}
              {selectedTemplate === 'custom' && (
                <>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Contenu gauche
                    </label>
                    <input
                      type="text"
                      value={footerData.left || ''}
                      onChange={(e) => handleFieldChange('left', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Ex: {documentTitle}"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Contenu central
                    </label>
                    <input
                      type="text"
                      value={footerData.center || ''}
                      onChange={(e) => handleFieldChange('center', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Ex: {documentType}"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Contenu droit
                    </label>
                    <input
                      type="text"
                      value={footerData.right || ''}
                      onChange={(e) => handleFieldChange('right', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Ex: Page {pageNumber} / {totalPages}"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Template personnalisé (HTML)
                    </label>
                    <textarea
                      value={footerData.customTemplate || ''}
                      onChange={(e) => handleFieldChange('customTemplate', e.target.value)}
                      rows={4}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="HTML personnalisé avec variables {pageNumber}, {totalPages}, etc."
                    />
                  </div>
                </>
              )}

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="showPageNumber"
                  checked={footerData.showPageNumber !== false}
                  onChange={(e) => handleFieldChange('showPageNumber', e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="showPageNumber" className="ml-2 block text-sm text-gray-900">
                  Afficher la numérotation des pages
                </label>
              </div>

              {/* Variables disponibles */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="text-sm font-medium text-gray-700 mb-2">Variables disponibles</h4>
                <div className="text-xs text-gray-600 space-y-1">
                  <p><code>{'{pageNumber}'}</code> - Numéro de la page courante</p>
                  <p><code>{'{totalPages}'}</code> - Nombre total de pages</p>
                  <p><code>{'{documentTitle}'}</code> - Titre du document</p>
                  <p><code>{'{documentType}'}</code> - Type de document</p>
                  <p><code>{'{documentIndice}'}</code> - Indice du document</p>
                  <p><code>{'{date}'}</code> - Date actuelle</p>
                  <p><code>{'{time}'}</code> - Heure actuelle</p>
                </div>
              </div>

              {/* Erreurs */}
              {errors.length > 0 && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <h4 className="text-sm font-medium text-red-800 mb-2">Erreurs de validation</h4>
                  <ul className="text-sm text-red-700 space-y-1">
                    {errors.map((error, index) => (
                      <li key={index}>• {error}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>

            {/* Aperçu */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Aperçu</h3>
              
              <div className="bg-white border border-gray-300 rounded-lg p-4 min-h-[200px]">
                <div className="bg-gray-50 border-2 border-dashed border-gray-300 rounded p-4 mb-4">
                  <p className="text-center text-gray-500 text-sm">Contenu de la page</p>
                </div>
                
                <div 
                  className="page-footer border-t border-gray-200 pt-2 text-xs text-gray-600"
                  dangerouslySetInnerHTML={{ __html: previewHtml }}
                />
              </div>
              
              <p className="text-xs text-gray-500">
                Cet aperçu montre comment le pied de page apparaîtra sur vos documents.
              </p>
            </div>
          </div>

          {/* Boutons d'action */}
          <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200 mt-6">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
            >
              Annuler
            </button>
            <button
              onClick={handleSave}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Sauvegarder
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

import { Node, mergeAttributes, textblockTypeInputRule } from '@tiptap/core'

export interface NumberedHeadingOptions {
  levels: number[]
  HTMLAttributes: Record<string, any>
}

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    numberedHeading: {
      /**
       * Définir un titre numéroté
       */
      setNumberedHeading: (attributes: { level: number; number?: string }) => ReturnType
      /**
       * Basculer vers un titre numéroté
       */
      toggleNumberedHeading: (attributes: { level: number; number?: string }) => ReturnType
      /**
       * Renuméroter tous les titres
       */
      renumberHeadings: () => ReturnType
    }
  }
}

export const NumberedHeadingExtension = Node.create<NumberedHeadingOptions>({
  name: 'numberedHeading',

  addOptions() {
    return {
      levels: [1, 2, 3, 4, 5, 6],
      HTMLAttributes: {},
    }
  },

  content: 'inline*',

  group: 'block',

  defining: true,

  addAttributes() {
    return {
      level: {
        default: 1,
        rendered: false,
      },
      number: {
        default: '',
        parseHTML: element => element.getAttribute('data-number'),
        renderHTML: attributes => {
          if (!attributes.number) {
            return {}
          }
          return {
            'data-number': attributes.number,
          }
        },
      },
    }
  },

  parseHTML() {
    return this.options.levels.map((level: number) => ({
      tag: `h${level}[data-numbered="true"]`,
      attrs: { level },
    }))
  },

  renderHTML({ node, HTMLAttributes }) {
    const hasLevel = this.options.levels.includes(node.attrs.level)
    const level = hasLevel ? node.attrs.level : this.options.levels[0]
    const number = node.attrs.number || ''

    return [
      `h${level}`,
      mergeAttributes(
        {
          'data-numbered': 'true',
          'data-level': level,
          'data-number': number,
          class: `orbis-heading orbis-heading-${level}`,
        },
        this.options.HTMLAttributes,
        HTMLAttributes
      ),
      number ? [
        'span',
        { class: 'heading-number' },
        `${number} `,
      ] : '',
      ['span', { class: 'heading-content' }, 0],
    ]
  },

  addCommands() {
    return {
      setNumberedHeading:
        attributes =>
        ({ commands }) => {
          if (!this.options.levels.includes(attributes.level)) {
            return false
          }

          return commands.setNode(this.name, attributes)
        },
      toggleNumberedHeading:
        attributes =>
        ({ commands }) => {
          if (!this.options.levels.includes(attributes.level)) {
            return false
          }

          return commands.toggleNode(this.name, 'paragraph', attributes)
        },
      renumberHeadings:
        () =>
        ({ tr, state }) => {
          const { doc } = state
          const headingCounts: Record<number, number> = {}
          
          // Initialiser les compteurs pour chaque niveau
          this.options.levels.forEach(level => {
            headingCounts[level] = 0
          })

          doc.descendants((node, pos) => {
            if (node.type.name === this.name) {
              const level = node.attrs.level
              
              // Réinitialiser les compteurs des niveaux inférieurs
              this.options.levels.forEach(l => {
                if (l > level) {
                  headingCounts[l] = 0
                }
              })
              
              // Incrémenter le compteur du niveau actuel
              headingCounts[level]++
              
              // Construire le numéro
              let number = ''
              for (let i = 1; i <= level; i++) {
                if (headingCounts[i] > 0) {
                  number += (number ? '.' : '') + headingCounts[i]
                }
              }
              
              // Mettre à jour l'attribut number
              tr.setNodeMarkup(pos, undefined, {
                ...node.attrs,
                number,
              })
            }
          })

          return true
        },
    }
  },

  addKeyboardShortcuts() {
    return this.options.levels.reduce(
      (items, level) => ({
        ...items,
        [`Mod-Alt-${level}`]: () => this.editor.commands.toggleNumberedHeading({ level }),
      }),
      {}
    )
  },

  addInputRules() {
    return this.options.levels.map(level => {
      return textblockTypeInputRule({
        find: new RegExp(`^(#{1,${level}})\\s$`),
        type: this.type,
        getAttributes: {
          level,
        },
      })
    })
  },
})

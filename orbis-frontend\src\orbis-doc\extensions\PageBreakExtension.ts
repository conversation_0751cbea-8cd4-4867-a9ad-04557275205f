import { Node, mergeAttributes } from '@tiptap/core'
import { ReactNodeViewRenderer } from '@tiptap/react'
import PageBreakNodeView from '../components/nodes/PageBreakNodeView'

export interface PageBreakOptions {
  HTMLAttributes: Record<string, any>
}

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    pageBreak: {
      /**
       * Insérer un saut de page
       */
      insertPageBreak: () => ReturnType
    }
  }
}

export const PageBreakExtension = Node.create<PageBreakOptions>({
  name: 'pageBreak',

  group: 'block',

  atom: true,

  addOptions() {
    return {
      HTMLAttributes: {},
    }
  },

  parseHTML() {
    return [
      {
        tag: 'div[data-type="page-break"]',
      },
    ]
  },

  renderHTML({ HTMLAttributes }) {
    return [
      'div',
      mergeAttributes(
        {
          'data-type': 'page-break',
          class: 'page-break',
        },
        this.options.HTMLAttributes,
        HTMLAttributes
      ),
      [
        'div',
        {
          class: 'page-break-line',
        },
      ],
    ]
  },

  addNodeView() {
    return ReactNodeViewRenderer(PageBreakNodeView)
  },

  addCommands() {
    return {
      insertPageBreak:
        () =>
        ({ commands }) => {
          return commands.insertContent({
            type: this.name,
          })
        },
    }
  },

  addKeyboardShortcuts() {
    return {
      'Mod-Enter': () => this.editor.commands.insertPageBreak(),
    }
  },
})

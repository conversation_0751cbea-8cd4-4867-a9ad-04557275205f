/* Styles pour les templates de pages de garde */

/* Styles de base pour toutes les pages de garde */
.cover-page-content {
  @apply h-full flex flex-col justify-between;
  min-height: calc(297mm - 60mm);
  padding: 20mm 0;
}

.cover-page-header {
  @apply flex justify-between items-start mb-8;
}

.cover-page-logo {
  max-height: 60px;
  max-width: 200px;
  object-fit: contain;
}

.cover-page-main {
  @apply flex-1 flex flex-col justify-center text-center;
}

.cover-page-footer {
  @apply text-center mt-8;
}

.cover-page-title {
  @apply text-4xl font-bold mb-6 text-gray-900;
  font-family: 'Times New Roman', serif;
  line-height: 1.2;
}

.cover-page-subtitle {
  @apply text-xl text-gray-600 mb-8;
  font-family: 'Times New Roman', serif;
  font-style: italic;
}

.document-author,
.document-company {
  @apply text-gray-700 mb-2;
  font-size: 14pt;
}

/* Template par défaut */
.cover-page-content .cover-page-meta {
  @apply space-y-3 text-gray-700 mb-8;
  font-size: 16pt;
}

.document-type {
  @apply font-semibold text-lg;
}

.document-date {
  @apply text-gray-600;
}

/* Template CCTP */
.cctp-template .document-classification {
  @apply text-right;
}

.classification-label {
  @apply text-sm font-semibold text-gray-600 uppercase tracking-wide;
  font-size: 10pt;
}

.classification-type {
  @apply text-2xl font-bold text-blue-800;
  font-family: 'Times New Roman', serif;
}

.cctp-meta {
  @apply bg-gray-50 border border-gray-200 rounded-lg p-6 mx-auto;
  max-width: 400px;
}

.meta-row {
  @apply flex justify-between items-center py-2 border-b border-gray-200 last:border-b-0;
}

.meta-label {
  @apply font-semibold text-gray-700;
  font-size: 14pt;
}

.meta-value {
  @apply text-gray-900;
  font-size: 14pt;
}

/* Template DPGF */
.dpgf-template .classification-type {
  @apply text-green-800;
}

.dpgf-meta {
  @apply mx-auto;
  max-width: 500px;
}

.meta-table {
  @apply w-full border-collapse;
}

.meta-table td {
  @apply py-3 px-4 border-b border-gray-200;
  font-size: 14pt;
}

.meta-table .meta-label {
  @apply bg-gray-50 font-semibold text-gray-700 w-1/3;
}

.meta-table .meta-value {
  @apply text-gray-900;
}

/* Template Rapport */
.rapport-template .document-type-badge {
  @apply inline-block bg-blue-600 text-white px-6 py-2 rounded-full mb-8;
  font-size: 14pt;
  font-weight: 600;
}

.rapport-meta {
  @apply space-y-2;
}

.document-indice {
  @apply text-lg font-semibold text-blue-600;
}

/* Template personnalisé */
.custom-template {
  @apply space-y-6;
}

.custom-field {
  @apply p-4 bg-gray-50 border border-gray-200 rounded;
  font-size: 14pt;
}

.custom-field[data-field="header"] {
  @apply bg-blue-50 border-blue-200 text-blue-800 font-semibold;
}

.custom-field[data-field="footer"] {
  @apply bg-gray-100 border-gray-300 text-gray-600 text-sm;
}

.custom-field[data-field="highlight"] {
  @apply bg-yellow-50 border-yellow-200 text-yellow-800 font-bold;
}

/* Styles responsives pour les pages de garde */
@media (max-width: 768px) {
  .cover-page-content {
    padding: 10mm 0;
  }
  
  .cover-page-title {
    @apply text-2xl;
  }
  
  .cover-page-subtitle {
    @apply text-lg;
  }
  
  .classification-type {
    @apply text-xl;
  }
  
  .cctp-meta,
  .dpgf-meta {
    max-width: 100%;
    margin: 0;
  }
  
  .meta-table td {
    @apply py-2 px-2;
    font-size: 12pt;
  }
  
  .document-type-badge {
    font-size: 12pt;
  }
}

/* Styles d'impression pour les pages de garde */
@media print {
  .cover-page-content {
    padding: 0;
    min-height: 100vh;
  }
  
  .cover-page-header {
    @apply mb-12;
  }
  
  .cover-page-footer {
    @apply mt-12;
  }
  
  .cover-page-logo {
    max-height: 80px;
    max-width: 250px;
  }
  
  .classification-type {
    @apply text-3xl;
  }
  
  .cover-page-title {
    @apply text-5xl;
  }
  
  .cover-page-subtitle {
    @apply text-2xl;
  }
}

// Types pour le module orbis-doc

export interface OrbisDocConfig {
  // Configuration générale
  mode: EditorMode
  readOnly?: boolean
  autoSave?: boolean
  autoSaveInterval?: number
  
  // Configuration des pages
  pageSize: PageSize
  margins: PageMargins
  showPageShadows?: boolean
  
  // Configuration du contenu
  documentTitle: string
  documentType: DocumentType
  documentIndice?: string
  
  // Configuration de la page de garde
  coverPage?: OrbisDocCoverPage
  
  // Callbacks
  onChange?: (content: OrbisDocContent) => void
  onSave?: (content: OrbisDocContent) => Promise<void>
  onPageChange?: (pageNumber: number) => void
}

export interface OrbisDocContent {
  // Contenu principal
  html: string
  json?: any // Format TipTap JSON
  
  // Métadonnées
  pages: OrbisDocPage[]
  totalPages: number
  wordCount: number
  characterCount: number
  
  // Page de garde
  coverPage?: OrbisDocCoverPage
}

export interface OrbisDocPage {
  id: string
  number: number
  content: string
  footer: OrbisDocFooter
  isBlank?: boolean
  isCoverPage?: boolean
}

export interface OrbisDocFooter {
  left?: string
  center?: string
  right?: string
  showPageNumber?: boolean
  customTemplate?: string
}

export interface OrbisDocCoverPage {
  title: string
  subtitle?: string
  documentType: string
  documentIndice?: string
  date?: string
  author?: string
  company?: string
  logo?: string
  customFields?: Record<string, string>
  template?: 'default' | 'cctp' | 'dpgf' | 'custom'
}

export interface PageMargins {
  top: number
  right: number
  bottom: number
  left: number
}

export type PageSize = 'A4' | 'A3' | 'Letter' | 'Legal'

export type DocumentType = 'CCTP' | 'DPGF' | 'RAPPORT' | 'DEVIS' | 'AUTRE'

export type EditorMode = 'edit' | 'view' | 'print'

export type DocumentFormat = 'html' | 'pdf' | 'docx' | 'json'

// Configuration des tailles de page en mm
export const PAGE_SIZES: Record<PageSize, { width: number; height: number }> = {
  A4: { width: 210, height: 297 },
  A3: { width: 297, height: 420 },
  Letter: { width: 216, height: 279 },
  Legal: { width: 216, height: 356 }
}

// Marges par défaut en mm
export const DEFAULT_MARGINS: PageMargins = {
  top: 25,
  right: 20,
  bottom: 25,
  left: 20
}

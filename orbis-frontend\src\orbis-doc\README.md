# Orbis-Doc

Module d'édition de documents techniques pour l'application ORBIS.

## Fonctionnalités

### ✅ Fonctionnalités principales
- **Pages visuelles** : Format A4 avec marges et ombres pour simuler des pages physiques
- **Pieds de page** : Chaque page a un pied de page avec titre du document et numéro de page
- **Pagination automatique** : Le contenu est automatiquement réparti sur plusieurs pages
- **Sauts de page manuels** : Bouton pour insérer des sauts de page
- **Impression optimisée** : Styles CSS spéciaux pour l'impression
- **Pages de garde** : Génération automatique de pages de garde personnalisables
- **Barre d'outils complète** : Tous les outils d'édition nécessaires

### 🎨 Personnalisation
- **Taille des pages** : A4, A3, Letter, Legal
- **Marges personnalisables** : Configuration des marges par document
- **Templates de page de garde** : Templates pour CCTP, DPGF, etc.
- **Styles d'impression** : Optimisation automatique pour l'impression

## Installation

Le module est intégré dans l'application ORBIS. Aucune installation séparée n'est nécessaire.

## Utilisation

### Utilisation de base

```tsx
import { OrbisDocEditor } from '@/orbis-doc'

function MyComponent() {
  const config = {
    mode: 'edit',
    pageSize: 'A4',
    margins: { top: 25, right: 20, bottom: 25, left: 20 },
    documentTitle: 'Mon Document',
    documentType: 'CCTP',
    onChange: (content) => console.log('Contenu modifié:', content),
    onSave: async (content) => {
      // Sauvegarder le document
    }
  }

  return (
    <OrbisDocEditor 
      config={config}
      initialContent={initialContent}
    />
  )
}
```

### Configuration avancée

```tsx
const advancedConfig = {
  mode: 'edit',
  pageSize: 'A4',
  margins: { top: 25, right: 20, bottom: 25, left: 20 },
  documentTitle: 'CCTP - Projet XYZ',
  documentType: 'CCTP',
  documentIndice: '01',
  autoSave: true,
  autoSaveInterval: 30000, // 30 secondes
  showPageShadows: true,
  coverPage: {
    title: 'Cahier des Clauses Techniques Particulières',
    subtitle: 'Projet de construction',
    documentType: 'CCTP',
    documentIndice: '01',
    date: new Date().toLocaleDateString('fr-FR'),
    author: 'Nom de l\'auteur',
    company: 'Nom de l\'entreprise',
    template: 'cctp'
  },
  onChange: (content) => {
    // Gérer les changements
  },
  onSave: async (content) => {
    // Sauvegarder
  },
  onPageChange: (pageNumber) => {
    // Changement de page
  }
}
```

## API

### Types principaux

- `OrbisDocConfig` : Configuration de l'éditeur
- `OrbisDocContent` : Contenu du document
- `OrbisDocPage` : Représentation d'une page
- `OrbisDocCoverPage` : Configuration de la page de garde

### Hooks disponibles

- `useOrbisDoc` : Hook principal de l'éditeur
- `useDocumentPagination` : Gestion de la pagination
- `useCoverPageGeneration` : Génération de pages de garde
- `useDocumentExport` : Export en PDF/Word

### Utilitaires

- `createDocument` : Création d'un nouveau document
- `exportToPDF` : Export en PDF
- `exportToWord` : Export en Word
- `validateDocument` : Validation du contenu

## Structure du module

```
orbis-doc/
├── components/          # Composants React
├── hooks/              # Hooks personnalisés
├── extensions/         # Extensions TipTap
├── styles/            # Styles CSS
├── types/             # Types TypeScript
├── utils/             # Utilitaires
└── index.ts           # Point d'entrée
```

## Intégration

Le module s'intègre facilement dans l'application ORBIS :

1. Import du module : `import { OrbisDocEditor } from '@/orbis-doc'`
2. Configuration selon les besoins
3. Utilisation dans vos composants

## Développement

Pour contribuer au module :

1. Respecter la structure existante
2. Ajouter des tests pour les nouvelles fonctionnalités
3. Documenter les nouvelles API
4. Suivre les conventions de code du projet

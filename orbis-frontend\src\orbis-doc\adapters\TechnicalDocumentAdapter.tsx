'use client'

import React, { useState, useCallback, useEffect } from 'react'
import { OrbisDocEditor } from '../index'
import { OrbisDocConfig, OrbisDocContent, DocumentType as OrbisDocumentType } from '../types'
import { DocumentType } from '@/types/technical-document'
import FooterConfigModal from '../components/FooterConfigModal'

// Interface pour correspondre à l'éditeur existant
interface TechnicalDocumentAdapterProps {
  value: string
  onChange: (content: string) => void
  documentType: DocumentType
  onTextSelection?: (selectedText: string) => void
  readOnly?: boolean
  workspaceName?: string
  workspaceLogo?: string
  onAddArticle?: (articleData: any) => void
  onSave?: () => Promise<void>
  onClose?: () => void
  hasUnsavedChanges?: boolean
  lotId?: number | null
  documentIndice?: string
  documentTitle?: string
}

export default function TechnicalDocumentAdapter({
  value,
  onChange,
  documentType,
  onTextSelection,
  readOnly = false,
  workspaceName = '',
  workspaceLogo = '',
  onAddArticle,
  onSave,
  onClose,
  hasUnsavedChanges = false,
  lotId,
  documentIndice = "01",
  documentTitle = 'Document'
}: TechnicalDocumentAdapterProps) {
  
  const [showFooterConfig, setShowFooterConfig] = useState(false)
  const [footerConfig, setFooterConfig] = useState({
    left: documentTitle,
    center: `${documentType}${documentIndice ? ` - Indice ${documentIndice}` : ''}`,
    right: 'Page {pageNumber} / {totalPages}',
    showPageNumber: true
  })

  // Convertir le DocumentType vers OrbisDocumentType
  const convertDocumentType = (type: DocumentType): OrbisDocumentType => {
    switch (type) {
      case DocumentType.CCTP:
        return 'CCTP'
      case DocumentType.DPGF:
        return 'DPGF'
      default:
        return 'AUTRE'
    }
  }

  // Configuration de l'éditeur orbis-doc
  const config: OrbisDocConfig = {
    mode: readOnly ? 'view' : 'edit',
    readOnly,
    autoSave: true,
    autoSaveInterval: 30000, // 30 secondes
    
    pageSize: 'A4',
    margins: {
      top: 25,
      right: 20,
      bottom: 25,
      left: 20
    },
    showPageShadows: true,
    
    documentTitle: documentTitle || 'Document',
    documentType: convertDocumentType(documentType),
    documentIndice,
    
    coverPage: {
      title: documentTitle || 'Document',
      subtitle: '',
      documentType: convertDocumentType(documentType),
      documentIndice,
      date: new Date().toLocaleDateString('fr-FR'),
      author: '',
      company: workspaceName,
      logo: workspaceLogo,
      template: documentType === DocumentType.CCTP ? 'cctp' : 
                documentType === DocumentType.DPGF ? 'dpgf' : 'default'
    },
    
    onChange: handleContentChange,
    onSave: handleSave,
    onPageChange: (pageNumber) => {
      // Callback optionnel pour les changements de page
      console.log('Page courante:', pageNumber)
    }
  }

  // Contenu initial
  const initialContent: OrbisDocContent = {
    html: value,
    json: null,
    pages: [],
    totalPages: 1,
    wordCount: 0,
    characterCount: 0
  }

  // Gérer les changements de contenu
  function handleContentChange(content: OrbisDocContent) {
    if (onChange) {
      onChange(content.html)
    }
    
    // Gérer la sélection de texte si nécessaire
    if (onTextSelection) {
      // Cette logique pourrait être améliorée pour détecter la sélection
      // onTextSelection(selectedText)
    }
  }

  // Gérer la sauvegarde
  const handleSave = useCallback(async (content: OrbisDocContent) => {
    if (onSave) {
      await onSave()
    }
  }, [onSave])

  // Gérer la configuration du pied de page
  const handleFooterConfigSave = (newFooterConfig: any) => {
    setFooterConfig(newFooterConfig)
    setShowFooterConfig(false)
  }

  // Mettre à jour la configuration quand les props changent
  useEffect(() => {
    setFooterConfig({
      left: documentTitle || 'Document',
      center: `${documentType}${documentIndice ? ` - Indice ${documentIndice}` : ''}`,
      right: 'Page {pageNumber} / {totalPages}',
      showPageNumber: true
    })
  }, [documentTitle, documentType, documentIndice])

  return (
    <>
      <OrbisDocEditor
        config={config}
        initialContent={initialContent}
        className="technical-document-adapter"
      />

      {/* Modal de configuration du pied de page */}
      {showFooterConfig && (
        <FooterConfigModal
          isOpen={showFooterConfig}
          onClose={() => setShowFooterConfig(false)}
          onSave={handleFooterConfigSave}
          currentFooter={footerConfig}
          config={config}
        />
      )}
    </>
  )
}

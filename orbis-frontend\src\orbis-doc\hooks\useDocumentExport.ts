import { useState, useCallback } from 'react'
import { Editor } from '@tiptap/react'
import { OrbisDocConfig, DocumentFormat } from '../types'

interface UseDocumentExportProps {
  editor: Editor | null
  config: OrbisDocConfig
}

export function useDocumentExport({ editor, config }: UseDocumentExportProps) {
  const [isExporting, setIsExporting] = useState(false)

  // Exporter en PDF
  const exportToPDF = useCallback(async () => {
    if (!editor) return

    setIsExporting(true)
    try {
      // Utiliser l'API d'impression du navigateur pour générer un PDF
      const printWindow = window.open('', '_blank')
      if (!printWindow) {
        throw new Error('Impossible d\'ouvrir la fenêtre d\'impression')
      }

      const content = editor.getHTML()
      const styles = `
        <style>
          @page {
            size: A4;
            margin: 25mm 20mm;
          }
          body {
            font-family: 'Times New Roman', serif;
            font-size: 12pt;
            line-height: 1.5;
            margin: 0;
            padding: 0;
          }
          .page-break {
            page-break-before: always;
          }
          .cover-page {
            page-break-after: always;
          }
        </style>
      `

      printWindow.document.write(`
        <!DOCTYPE html>
        <html>
          <head>
            <title>${config.documentTitle}</title>
            ${styles}
          </head>
          <body>
            ${content}
          </body>
        </html>
      `)
      
      printWindow.document.close()
      printWindow.focus()
      printWindow.print()
      
    } catch (error) {
      console.error('Erreur lors de l\'export PDF:', error)
      throw error
    } finally {
      setIsExporting(false)
    }
  }, [editor, config])

  // Exporter en Word (HTML compatible)
  const exportToWord = useCallback(async () => {
    if (!editor) return

    setIsExporting(true)
    try {
      const content = editor.getHTML()
      const blob = new Blob([`
        <!DOCTYPE html>
        <html>
          <head>
            <meta charset="utf-8">
            <title>${config.documentTitle}</title>
            <style>
              body {
                font-family: 'Times New Roman', serif;
                font-size: 12pt;
                line-height: 1.5;
                margin: 2.5cm 2cm;
              }
            </style>
          </head>
          <body>
            ${content}
          </body>
        </html>
      `], { type: 'application/msword' })

      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `${config.documentTitle}.doc`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)
      
    } catch (error) {
      console.error('Erreur lors de l\'export Word:', error)
      throw error
    } finally {
      setIsExporting(false)
    }
  }, [editor, config])

  // Exporter en HTML
  const exportToHTML = useCallback(async () => {
    if (!editor) return

    setIsExporting(true)
    try {
      const content = editor.getHTML()
      const blob = new Blob([content], { type: 'text/html' })
      
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `${config.documentTitle}.html`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)
      
    } catch (error) {
      console.error('Erreur lors de l\'export HTML:', error)
      throw error
    } finally {
      setIsExporting(false)
    }
  }, [editor, config])

  // Exporter en JSON (format TipTap)
  const exportToJSON = useCallback(async () => {
    if (!editor) return

    setIsExporting(true)
    try {
      const content = JSON.stringify(editor.getJSON(), null, 2)
      const blob = new Blob([content], { type: 'application/json' })
      
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `${config.documentTitle}.json`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)
      
    } catch (error) {
      console.error('Erreur lors de l\'export JSON:', error)
      throw error
    } finally {
      setIsExporting(false)
    }
  }, [editor, config])

  // Exporter selon le format spécifié
  const exportDocument = useCallback(async (format: DocumentFormat) => {
    switch (format) {
      case 'pdf':
        return exportToPDF()
      case 'docx':
        return exportToWord()
      case 'html':
        return exportToHTML()
      case 'json':
        return exportToJSON()
      default:
        throw new Error(`Format d'export non supporté: ${format}`)
    }
  }, [exportToPDF, exportToWord, exportToHTML, exportToJSON])

  return {
    isExporting,
    exportToPDF,
    exportToWord,
    exportToHTML,
    exportToJSON,
    exportDocument
  }
}

import { useState, useCallback, useEffect, useRef } from 'react'
import { Editor } from '@tiptap/react'
import { OrbisDocConfig, OrbisDocPage, PAGE_SIZES, DEFAULT_MARGINS } from '../types'
import { calculatePageHeight } from '../utils/pageCalculator'

export function useDocumentPagination(
  editor: Editor | null,
  config: OrbisDocConfig
) {
  const [pages, setPages] = useState<OrbisDocPage[]>([])
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const observerRef = useRef<ResizeObserver | null>(null)
  const containerRef = useRef<HTMLDivElement | null>(null)

  // Calculer la hauteur de page disponible
  const getPageDimensions = useCallback(() => {
    const pageSize = config.pageSize || 'A4'
    const margins = config.margins || DEFAULT_MARGINS
    const pageDimensions = PAGE_SIZES[pageSize]
    
    return {
      width: pageDimensions.width,
      height: pageDimensions.height,
      contentHeight: pageDimensions.height - margins.top - margins.bottom,
      contentWidth: pageDimensions.width - margins.left - margins.right,
      margins
    }
  }, [config.pageSize, config.margins])

  // Mettre à jour la pagination
  const updatePagination = useCallback(() => {
    if (!editor) return

    const { contentHeight } = getPageDimensions()
    const content = editor.getHTML()
    
    // Créer un élément temporaire pour mesurer le contenu
    const tempDiv = document.createElement('div')
    tempDiv.style.position = 'absolute'
    tempDiv.style.visibility = 'hidden'
    tempDiv.style.width = '210mm' // Largeur A4
    tempDiv.style.fontSize = '12pt'
    tempDiv.style.lineHeight = '1.5'
    tempDiv.innerHTML = content
    
    document.body.appendChild(tempDiv)
    
    try {
      const contentHeightPx = tempDiv.scrollHeight
      const pageHeightPx = calculatePageHeight(contentHeight)
      
      // Calculer le nombre de pages nécessaires
      const calculatedPages = Math.max(1, Math.ceil(contentHeightPx / pageHeightPx))
      
      // Créer les objets de page
      const newPages: OrbisDocPage[] = []
      
      for (let i = 1; i <= calculatedPages; i++) {
        const page: OrbisDocPage = {
          id: `page-${i}`,
          number: i,
          content: '', // Le contenu sera géré par TipTap
          footer: {
            left: config.documentTitle,
            center: '',
            right: `Page ${i} / ${calculatedPages}`,
            showPageNumber: true
          },
          isBlank: false,
          isCoverPage: false
        }
        
        newPages.push(page)
      }
      
      // Vérifier s'il y a une page de garde
      const doc = editor.state.doc
      let hasCoverPage = false
      
      doc.descendants((node) => {
        if (node.type.name === 'coverPage') {
          hasCoverPage = true
          return false
        }
      })
      
      if (hasCoverPage) {
        // Ajouter une page de garde au début
        newPages.unshift({
          id: 'cover-page',
          number: 0,
          content: '',
          footer: {
            left: '',
            center: '',
            right: '',
            showPageNumber: false
          },
          isBlank: false,
          isCoverPage: true
        })
        
        // Ajouter une page vierge après la page de garde
        newPages.splice(1, 0, {
          id: 'blank-page',
          number: 0,
          content: '',
          footer: {
            left: '',
            center: '',
            right: '',
            showPageNumber: false
          },
          isBlank: true,
          isCoverPage: false
        })
        
        // Renuméroter les pages de contenu
        for (let i = 2; i < newPages.length; i++) {
          newPages[i].number = i - 1
          newPages[i].footer.right = `Page ${i - 1} / ${calculatedPages}`
        }
      }
      
      setPages(newPages)
      setTotalPages(newPages.length)
      
    } finally {
      document.body.removeChild(tempDiv)
    }
  }, [editor, config, getPageDimensions])

  // Navigation entre les pages
  const goToPage = useCallback((pageNumber: number) => {
    if (pageNumber >= 1 && pageNumber <= totalPages) {
      setCurrentPage(pageNumber)
    }
  }, [totalPages])

  const nextPage = useCallback(() => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1)
    }
  }, [currentPage, totalPages])

  const previousPage = useCallback(() => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1)
    }
  }, [currentPage])

  // Observer les changements de contenu pour mettre à jour la pagination
  useEffect(() => {
    if (!editor) return

    const handleUpdate = () => {
      // Débounce pour éviter trop de recalculs
      setTimeout(updatePagination, 100)
    }

    editor.on('update', handleUpdate)
    
    return () => {
      editor.off('update', handleUpdate)
    }
  }, [editor, updatePagination])

  // Observer les changements de taille du conteneur
  useEffect(() => {
    if (!containerRef.current) return

    observerRef.current = new ResizeObserver(() => {
      updatePagination()
    })

    observerRef.current.observe(containerRef.current)

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect()
      }
    }
  }, [updatePagination])

  // Mise à jour initiale
  useEffect(() => {
    if (editor) {
      updatePagination()
    }
  }, [editor, updatePagination])

  return {
    pages,
    currentPage,
    totalPages,
    containerRef,
    goToPage,
    nextPage,
    previousPage,
    updatePagination,
    getPageDimensions
  }
}

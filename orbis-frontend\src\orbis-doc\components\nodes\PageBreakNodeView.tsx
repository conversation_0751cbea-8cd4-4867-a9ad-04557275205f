'use client'

import React from 'react'
import { NodeViewWrapper } from '@tiptap/react'

interface PageBreakNodeViewProps {
  node: any
  updateAttributes: (attributes: Record<string, any>) => void
  deleteNode: () => void
}

export default function PageBreakNodeView({
  node,
  updateAttributes,
  deleteNode
}: PageBreakNodeViewProps) {
  
  const handleDelete = () => {
    deleteNode()
  }

  return (
    <NodeViewWrapper className="page-break-wrapper">
      <div className="page-break-container group relative">
        {/* Ligne de saut de page */}
        <div className="page-break-line border-t-2 border-dashed border-gray-300 relative">
          {/* Label du saut de page */}
          <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white px-3 py-1 text-xs text-gray-500 border border-gray-300 rounded">
            Saut de page
          </div>
          
          {/* Bouton de suppression (visible au hover) */}
          <button
            onClick={handleDelete}
            className="absolute top-0 right-4 transform -translate-y-1/2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs opacity-0 group-hover:opacity-100 transition-opacity hover:bg-red-600"
            title="Supprimer le saut de page"
          >
            ×
          </button>
        </div>
        
        {/* Zone de prévisualisation de la nouvelle page */}
        <div className="page-break-preview mt-4 p-4 bg-gray-50 border border-gray-200 rounded text-center text-sm text-gray-600">
          <div className="flex items-center justify-center space-x-2">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <span>Nouvelle page</span>
          </div>
        </div>
      </div>
    </NodeViewWrapper>
  )
}

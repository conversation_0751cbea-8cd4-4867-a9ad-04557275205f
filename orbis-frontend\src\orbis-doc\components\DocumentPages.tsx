'use client'

import React, { useEffect, useRef } from 'react'
import { Editor, EditorContent } from '@tiptap/react'
import { OrbisDocConfig, OrbisDocPage } from '../types'
import { generatePageStyles } from '../utils/pageCalculator'

interface DocumentPagesProps {
  editor: Editor
  pages: OrbisDocPage[]
  config: OrbisDocConfig
  currentPage: number
}

export default function DocumentPages({
  editor,
  pages,
  config,
  currentPage
}: DocumentPagesProps) {

  const containerRef = useRef<HTMLDivElement>(null)
  const pageSize = config.pageSize || 'A4'
  const margins = config.margins
  const showShadows = config.showPageShadows !== false

  // Générer les styles de page
  const pageStyles = generatePageStyles(pageSize, margins, showShadows)

  // Vérifier s'il y a une page de garde dans le contenu
  const hasCoverPage = editor?.state.doc.content.content.some(
    (node: any) => node.type.name === 'coverPage'
  ) || false

  // Vérifier s'il y a des sauts de page
  const hasPageBreaks = editor?.state.doc.content.content.some(
    (node: any) => node.type.name === 'pageBreak'
  ) || false

  // Effet pour faire défiler vers la page courante
  useEffect(() => {
    if (containerRef.current && currentPage > 1) {
      const pageElements = containerRef.current.querySelectorAll('.document-page')
      const targetPage = pageElements[currentPage - 1]
      if (targetPage) {
        targetPage.scrollIntoView({ behavior: 'smooth', block: 'start' })
      }
    }
  }, [currentPage])

  return (
    <div className="document-pages-container" ref={containerRef}>
      {/* Page de garde (si présente) */}
      {hasCoverPage && (
        <div className="document-page cover-page-wrapper" style={pageStyles}>
          <div className="page-content">
            {/* La page de garde sera rendue par TipTap */}
          </div>
          {/* Pas de pied de page pour la page de garde */}
        </div>
      )}

      {/* Page vierge après la page de garde */}
      {hasCoverPage && (
        <div className="document-page blank-page" style={pageStyles}>
          <div className="page-content">
            <div className="blank-page-text">
              Page intentionnellement laissée vierge
            </div>
          </div>
          {/* Pas de pied de page pour la page vierge */}
        </div>
      )}

      {/* Page principale avec l'éditeur */}
      <div className="document-page" style={pageStyles}>
        <div className="page-content">
          <EditorContent editor={editor} />
        </div>

        {/* Pied de page */}
        <div className="page-footer">
          <div className="footer-content">
            <div className="footer-left">
              {config.documentTitle}
            </div>
            <div className="footer-center">
              {config.documentType}
              {config.documentIndice && ` - Indice ${config.documentIndice}`}
            </div>
            <div className="footer-right">
              Page {hasCoverPage ? currentPage - 1 : currentPage} / {Math.max(1, pages.length - (hasCoverPage ? 2 : 0))}
            </div>
          </div>
        </div>
      </div>

      {/* Pages supplémentaires générées par les sauts de page */}
      {hasPageBreaks && pages.slice(hasCoverPage ? 3 : 1).map((page, index) => (
        <div key={page.id} className="document-page" style={pageStyles}>
          <div className="page-content">
            {/* Le contenu sera géré par la pagination automatique */}
          </div>

          {/* Pied de page */}
          <div className="page-footer">
            <div className="footer-content">
              <div className="footer-left">
                {page.footer.left || config.documentTitle}
              </div>
              <div className="footer-center">
                {page.footer.center || config.documentType}
              </div>
              <div className="footer-right">
                {page.footer.showPageNumber !== false && (
                  page.footer.right || `Page ${page.number} / ${pages.length}`
                )}
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}

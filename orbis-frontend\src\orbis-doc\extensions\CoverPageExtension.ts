import { Node, mergeAttributes } from '@tiptap/core'
import { ReactNodeViewRenderer } from '@tiptap/react'
import CoverPageNodeView from '../components/nodes/CoverPageNodeView'

export interface CoverPageOptions {
  HTMLAttributes: Record<string, any>
}

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    coverPage: {
      /**
       * Insérer une page de garde
       */
      insertCoverPage: (attributes?: {
        title?: string
        subtitle?: string
        documentType?: string
        documentIndice?: string
        date?: string
        author?: string
        company?: string
        logo?: string
        template?: string
      }) => ReturnType
    }
  }
}

export const CoverPageExtension = Node.create<CoverPageOptions>({
  name: 'coverPage',

  group: 'block',

  atom: true,

  addOptions() {
    return {
      HTMLAttributes: {},
    }
  },

  addAttributes() {
    return {
      title: {
        default: '',
        parseHTML: element => element.getAttribute('data-title'),
        renderHTML: attributes => ({
          'data-title': attributes.title,
        }),
      },
      subtitle: {
        default: '',
        parseHTML: element => element.getAttribute('data-subtitle'),
        renderHTML: attributes => ({
          'data-subtitle': attributes.subtitle,
        }),
      },
      documentType: {
        default: '',
        parseHTML: element => element.getAttribute('data-document-type'),
        renderHTML: attributes => ({
          'data-document-type': attributes.documentType,
        }),
      },
      documentIndice: {
        default: '',
        parseHTML: element => element.getAttribute('data-document-indice'),
        renderHTML: attributes => ({
          'data-document-indice': attributes.documentIndice,
        }),
      },
      date: {
        default: '',
        parseHTML: element => element.getAttribute('data-date'),
        renderHTML: attributes => ({
          'data-date': attributes.date,
        }),
      },
      author: {
        default: '',
        parseHTML: element => element.getAttribute('data-author'),
        renderHTML: attributes => ({
          'data-author': attributes.author,
        }),
      },
      company: {
        default: '',
        parseHTML: element => element.getAttribute('data-company'),
        renderHTML: attributes => ({
          'data-company': attributes.company,
        }),
      },
      logo: {
        default: '',
        parseHTML: element => element.getAttribute('data-logo'),
        renderHTML: attributes => ({
          'data-logo': attributes.logo,
        }),
      },
      template: {
        default: 'default',
        parseHTML: element => element.getAttribute('data-template'),
        renderHTML: attributes => ({
          'data-template': attributes.template,
        }),
      },
    }
  },

  parseHTML() {
    return [
      {
        tag: 'div[data-type="cover-page"]',
      },
    ]
  },

  renderHTML({ HTMLAttributes }) {
    return [
      'div',
      mergeAttributes(
        {
          'data-type': 'cover-page',
          class: 'cover-page',
        },
        this.options.HTMLAttributes,
        HTMLAttributes
      ),
    ]
  },

  addNodeView() {
    return ReactNodeViewRenderer(CoverPageNodeView)
  },

  addCommands() {
    return {
      insertCoverPage:
        (attributes = {}) =>
        ({ commands }) => {
          return commands.insertContent({
            type: this.name,
            attrs: {
              title: attributes.title || 'Titre du document',
              subtitle: attributes.subtitle || '',
              documentType: attributes.documentType || 'Document',
              documentIndice: attributes.documentIndice || '01',
              date: attributes.date || new Date().toLocaleDateString('fr-FR'),
              author: attributes.author || '',
              company: attributes.company || '',
              logo: attributes.logo || '',
              template: attributes.template || 'default',
            },
          })
        },
    }
  },
})

import { useState, useCallback } from 'react'
import { Editor } from '@tiptap/react'
import { OrbisDocCoverPage } from '../types'

interface UseCoverPageGenerationProps {
  editor: Editor | null
}

export function useCoverPageGeneration({ editor }: UseCoverPageGenerationProps) {
  const [isGenerating, setIsGenerating] = useState(false)

  // Templates de pages de garde
  const templates = {
    default: {
      name: 'Par défaut',
      description: 'Template simple et épuré'
    },
    cctp: {
      name: 'CCTP',
      description: 'Template spécialisé pour les CCTP'
    },
    dpgf: {
      name: 'DPGF',
      description: 'Template spécialisé pour les DPGF'
    },
    rapport: {
      name: 'Rapport',
      description: 'Template pour les rapports techniques'
    },
    custom: {
      name: 'Personnalisé',
      description: 'Template entièrement personnalisable'
    }
  }

  // Générer le HTML d'une page de garde selon le template
  const generateCoverPageHTML = useCallback((coverPage: OrbisDocCoverPage): string => {
    const { template = 'default' } = coverPage

    switch (template) {
      case 'cctp':
        return generateCCTPTemplate(coverPage)
      case 'dpgf':
        return generateDPGFTemplate(coverPage)
      case 'rapport':
        return generateRapportTemplate(coverPage)
      case 'custom':
        return generateCustomTemplate(coverPage)
      default:
        return generateDefaultTemplate(coverPage)
    }
  }, [])

  // Template par défaut
  const generateDefaultTemplate = (coverPage: OrbisDocCoverPage): string => {
    return `
      <div class="cover-page-content">
        <div class="cover-page-header">
          ${coverPage.logo ? `<img src="${coverPage.logo}" alt="Logo" class="cover-page-logo" />` : ''}
        </div>
        
        <div class="cover-page-main">
          <h1 class="cover-page-title">${coverPage.title}</h1>
          ${coverPage.subtitle ? `<h2 class="cover-page-subtitle">${coverPage.subtitle}</h2>` : ''}
          
          <div class="cover-page-meta">
            <p class="document-type">${coverPage.documentType}${coverPage.documentIndice ? ` - Indice ${coverPage.documentIndice}` : ''}</p>
            ${coverPage.date ? `<p class="document-date">${coverPage.date}</p>` : ''}
          </div>
        </div>
        
        <div class="cover-page-footer">
          ${coverPage.author ? `<p class="document-author">Rédigé par : ${coverPage.author}</p>` : ''}
          ${coverPage.company ? `<p class="document-company">${coverPage.company}</p>` : ''}
        </div>
      </div>
    `
  }

  // Template CCTP
  const generateCCTPTemplate = (coverPage: OrbisDocCoverPage): string => {
    return `
      <div class="cover-page-content cctp-template">
        <div class="cover-page-header">
          ${coverPage.logo ? `<img src="${coverPage.logo}" alt="Logo" class="cover-page-logo" />` : ''}
          <div class="document-classification">
            <p class="classification-label">CAHIER DES CLAUSES TECHNIQUES PARTICULIÈRES</p>
            <p class="classification-type">CCTP</p>
          </div>
        </div>
        
        <div class="cover-page-main">
          <h1 class="cover-page-title">${coverPage.title}</h1>
          ${coverPage.subtitle ? `<h2 class="cover-page-subtitle">${coverPage.subtitle}</h2>` : ''}
          
          <div class="cover-page-meta cctp-meta">
            <div class="meta-row">
              <span class="meta-label">Document :</span>
              <span class="meta-value">${coverPage.documentType}</span>
            </div>
            ${coverPage.documentIndice ? `
              <div class="meta-row">
                <span class="meta-label">Indice :</span>
                <span class="meta-value">${coverPage.documentIndice}</span>
              </div>
            ` : ''}
            ${coverPage.date ? `
              <div class="meta-row">
                <span class="meta-label">Date :</span>
                <span class="meta-value">${coverPage.date}</span>
              </div>
            ` : ''}
          </div>
        </div>
        
        <div class="cover-page-footer">
          ${coverPage.author ? `<p class="document-author">Rédigé par : ${coverPage.author}</p>` : ''}
          ${coverPage.company ? `<p class="document-company">${coverPage.company}</p>` : ''}
        </div>
      </div>
    `
  }

  // Template DPGF
  const generateDPGFTemplate = (coverPage: OrbisDocCoverPage): string => {
    return `
      <div class="cover-page-content dpgf-template">
        <div class="cover-page-header">
          ${coverPage.logo ? `<img src="${coverPage.logo}" alt="Logo" class="cover-page-logo" />` : ''}
          <div class="document-classification">
            <p class="classification-label">DÉTAIL DES PRIX GLOBAL ET FORFAITAIRE</p>
            <p class="classification-type">DPGF</p>
          </div>
        </div>
        
        <div class="cover-page-main">
          <h1 class="cover-page-title">${coverPage.title}</h1>
          ${coverPage.subtitle ? `<h2 class="cover-page-subtitle">${coverPage.subtitle}</h2>` : ''}
          
          <div class="cover-page-meta dpgf-meta">
            <table class="meta-table">
              <tr>
                <td class="meta-label">Document :</td>
                <td class="meta-value">${coverPage.documentType}</td>
              </tr>
              ${coverPage.documentIndice ? `
                <tr>
                  <td class="meta-label">Indice :</td>
                  <td class="meta-value">${coverPage.documentIndice}</td>
                </tr>
              ` : ''}
              ${coverPage.date ? `
                <tr>
                  <td class="meta-label">Date :</td>
                  <td class="meta-value">${coverPage.date}</td>
                </tr>
              ` : ''}
            </table>
          </div>
        </div>
        
        <div class="cover-page-footer">
          ${coverPage.author ? `<p class="document-author">Rédigé par : ${coverPage.author}</p>` : ''}
          ${coverPage.company ? `<p class="document-company">${coverPage.company}</p>` : ''}
        </div>
      </div>
    `
  }

  // Template Rapport
  const generateRapportTemplate = (coverPage: OrbisDocCoverPage): string => {
    return `
      <div class="cover-page-content rapport-template">
        <div class="cover-page-header">
          ${coverPage.logo ? `<img src="${coverPage.logo}" alt="Logo" class="cover-page-logo" />` : ''}
        </div>
        
        <div class="cover-page-main">
          <div class="document-type-badge">
            <span>${coverPage.documentType}</span>
          </div>
          
          <h1 class="cover-page-title">${coverPage.title}</h1>
          ${coverPage.subtitle ? `<h2 class="cover-page-subtitle">${coverPage.subtitle}</h2>` : ''}
          
          <div class="cover-page-meta rapport-meta">
            ${coverPage.documentIndice ? `<p class="document-indice">Version ${coverPage.documentIndice}</p>` : ''}
            ${coverPage.date ? `<p class="document-date">${coverPage.date}</p>` : ''}
          </div>
        </div>
        
        <div class="cover-page-footer">
          ${coverPage.author ? `<p class="document-author">${coverPage.author}</p>` : ''}
          ${coverPage.company ? `<p class="document-company">${coverPage.company}</p>` : ''}
        </div>
      </div>
    `
  }

  // Template personnalisé
  const generateCustomTemplate = (coverPage: OrbisDocCoverPage): string => {
    // Pour le template personnalisé, on utilise les customFields
    const customFields = coverPage.customFields || {}
    
    return `
      <div class="cover-page-content custom-template">
        ${Object.entries(customFields).map(([key, value]) => 
          `<div class="custom-field" data-field="${key}">${value}</div>`
        ).join('')}
        
        <div class="cover-page-main">
          <h1 class="cover-page-title">${coverPage.title}</h1>
          ${coverPage.subtitle ? `<h2 class="cover-page-subtitle">${coverPage.subtitle}</h2>` : ''}
        </div>
      </div>
    `
  }

  // Générer une page de garde
  const generateCoverPage = useCallback(async (coverPageData: OrbisDocCoverPage) => {
    if (!editor) return

    setIsGenerating(true)

    try {
      // Insérer la page de garde au début du document
      editor.commands.insertContentAt(0, {
        type: 'coverPage',
        attrs: coverPageData,
      })

      // Ajouter un saut de page après la page de garde
      editor.commands.insertContentAt(1, {
        type: 'pageBreak',
      })

    } catch (error) {
      console.error('Erreur lors de la génération de la page de garde:', error)
      throw error
    } finally {
      setIsGenerating(false)
    }
  }, [editor])

  return {
    generateCoverPage,
    generateCoverPageHTML,
    templates,
    isGenerating
  }
}

// Module orbis-doc - Éditeur de documents techniques
// Point d'entrée principal du module

export { default as OrbisDocEditor } from './components/OrbisDocEditor'
export { default as OrbisDocViewer } from './components/OrbisDocViewer'

// Types
export type {
  OrbisDocConfig,
  OrbisDocContent,
  OrbisDocPage,
  OrbisDocFooter,
  OrbisDocCoverPage,
  DocumentFormat,
  PageSize,
  EditorMode
} from './types'

// Hooks
export {
  useOrbisDoc,
  useDocumentPagination,
  useCoverPageGeneration,
  useDocumentExport
} from './hooks'

// Utilitaires
export {
  createDocument,
  exportToPDF,
  exportToWord,
  validateDocument
} from './utils'

'use client'

import React, { useState, useEffect, useRef } from 'react'
import { useSearchParams, useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import ProtectedRoute from '@/components/ProtectedRoute'
import { ModernDocumentEditor } from '@/components/editor'
import PaginatedDocumentEditor from '@/components/editor/PaginatedDocumentEditor'
import { DocumentType, TechnicalDocumentResponse } from '@/types/technical-document'
import { api } from '@/lib/api'

function DocumentsTechniquesContent() {
  const { user } = useAuth()
  const searchParams = useSearchParams()
  const router = useRouter()

  // Récupérer les IDs depuis l'URL
  const selectedProjectId = searchParams.get('project_id') ? parseInt(searchParams.get('project_id')!) : null
  const selectedLotId = searchParams.get('lot_id') ? parseInt(searchParams.get('lot_id')!) : null
  const documentIdFromUrl = searchParams.get('document_id') ? parseInt(searchParams.get('document_id')!) : null

  // États
  const [selectedDocument, setSelectedDocument] = useState<TechnicalDocumentResponse | null>(null)
  const [isSaving, setIsSaving] = useState(false)
  const [hasPendingChanges, setHasPendingChanges] = useState(false)
  const [lastSaved, setLastSaved] = useState<Date | null>(null)
  const [enhancing, setEnhancing] = useState(false)
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)
  const [isPaginatedMode, setIsPaginatedMode] = useState(true)
  const [isCreating, setIsCreating] = useState(false)
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [newDocumentName, setNewDocumentName] = useState('')
  const [newDocumentType, setNewDocumentType] = useState<DocumentType>(DocumentType.CCTP)

  // Refs pour la sauvegarde automatique
  const saveTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const contentRef = useRef<string>('')

  // Fonctions API
  const updateDocument = async (id: number, data: { content: string }) => {
    return await api.updateTechnicalDocument(id, data)
  }

  const createDocument = async (data: { name: string, type_document: DocumentType, lot_id: number }) => {
    return await api.createTechnicalDocument(data)
  }



  // Gérer les modifications du contenu
  const handleContentChange = (content: string) => {
    contentRef.current = content
    setHasUnsavedChanges(true)
    setHasPendingChanges(true)
  }

  // Fonction de sauvegarde
  const handleSave = async () => {
    if (!selectedDocument || !contentRef.current) return

    try {
      setIsSaving(true)
      await updateDocument(selectedDocument.id, { content: contentRef.current })
      setHasUnsavedChanges(false)
      setHasPendingChanges(false)
      setLastSaved(new Date())
      console.log('✅ Document sauvegardé')
    } catch (error) {
      console.error('❌ Erreur lors de la sauvegarde:', error)
      alert('Erreur lors de la sauvegarde du document')
    } finally {
      setIsSaving(false)
    }
  }

  // Fonction pour fermer l'éditeur
  const handleClose = () => {
    // Retourner au lot en cours
    if (selectedProjectId && selectedLotId) {
      router.push(`/projects/${selectedProjectId}/lots/${selectedLotId}`)
    } else if (selectedProjectId) {
      router.push(`/projects/${selectedProjectId}`)
    } else {
      router.push('/projects')
    }
  }

  // Gérer la sélection de texte
  const handleTextSelection = (selectedText: string) => {
    // Cette fonction peut être utilisée pour des fonctionnalités futures
    console.log('Texte sélectionné:', selectedText)
  }

  // Gérer l'ajout d'article (optionnel - l'insertion se fait maintenant dans l'éditeur)
  const handleArticleSubmit = async (articleData: any) => {
    // Cette fonction peut être utilisée pour des traitements supplémentaires
    // L'insertion du contenu se fait maintenant directement dans ModernDocumentEditor
    console.log('📝 Article ajouté via l\'éditeur:', articleData)

    // Marquer le document comme ayant des modifications non sauvegardées
    setHasUnsavedChanges(true)
  }

  // Gérer la création d'un nouveau document
  const handleCreateDocument = async () => {
    if (!newDocumentName.trim()) return

    try {
      setIsCreating(true)

      // Créer le document avec un lot_id par défaut
      // Si pas de lot_id dans l'URL, on utilise 1 par défaut
      const defaultLotId = selectedLotId || 1

      const newDoc = await createDocument({
        name: newDocumentName.trim(),
        type_document: newDocumentType,
        lot_id: defaultLotId
      }) as TechnicalDocumentResponse

      // Charger directement le document créé
      setSelectedDocument(newDoc)
      contentRef.current = newDoc.content || ''

      // Mettre à jour l'URL pour inclure le nouveau document
      const newUrl = `/documents-techniques?project_id=${selectedProjectId || 1}&lot_id=${defaultLotId}&document_id=${newDoc.id}`
      router.replace(newUrl)

      // Réinitialiser le formulaire
      setNewDocumentName('')
      setShowCreateForm(false)

      console.log('✅ Document créé avec succès:', newDoc)
    } catch (error) {
      console.error('❌ Erreur lors de la création du document:', error)
      alert('Erreur lors de la création du document. Vérifiez que vous avez accès à un projet et un lot.')
    } finally {
      setIsCreating(false)
    }
  }

  // Gérer le démarrage de la création
  const handleStartCreate = (type: DocumentType) => {
    setNewDocumentType(type)
    setNewDocumentName(`Nouveau ${type} - ${new Date().toLocaleDateString('fr-FR')}`)
    setShowCreateForm(true)
  }



  // Charger le document depuis l'URL au démarrage
  useEffect(() => {
    const loadDocument = async () => {
      if (documentIdFromUrl && !selectedDocument) {
        try {
          console.log('🔄 Chargement du document depuis l\'URL:', documentIdFromUrl)
          const doc = await api.getTechnicalDocument(documentIdFromUrl) as TechnicalDocumentResponse
          setSelectedDocument(doc)
          console.log('✅ Document chargé:', doc)
        } catch (error) {
          console.error('❌ Erreur lors du chargement du document:', error)
        }
      }
    }

    loadDocument()
  }, [documentIdFromUrl, selectedDocument])

  // Nettoyage
  useEffect(() => {
    return () => {
      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current)
      }
    }
  }, [])

  // Charger le document si un document_id est fourni
  useEffect(() => {
    const loadDocument = async () => {
      if (documentIdFromUrl) {
        try {
          const doc = await api.getTechnicalDocument(documentIdFromUrl) as TechnicalDocumentResponse
          setSelectedDocument(doc)
          contentRef.current = doc.content || ''
        } catch (error) {
          console.error('Erreur lors du chargement du document:', error)
        }
      }
    }

    loadDocument()
  }, [documentIdFromUrl])

  // Si on a un document_id mais pas de project_id/lot_id, on peut quand même essayer de charger
  // Si on n'a rien, on affiche l'interface de création

  return (
    <div className="h-screen bg-gray-50 flex flex-col">
        {selectedDocument ? (
          // Mode édition avec choix entre éditeur normal et paginé
          <div className="h-full flex flex-col">
            {/* Barre de basculement de mode */}
            <div className="bg-white border-b border-gray-200 px-4 py-2 flex items-center justify-between">
              <div className="flex items-center gap-4">
                <h2 className="font-semibold text-gray-900">
                  {selectedDocument.name}
                </h2>
                <span className="text-sm text-gray-500">
                  {selectedDocument.type_document}
                </span>
              </div>

              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-600">Mode d'affichage:</span>
                <button
                  onClick={() => setIsPaginatedMode(false)}
                  className={`px-3 py-1 text-sm rounded ${
                    !isPaginatedMode
                      ? 'bg-blue-100 text-blue-700 border border-blue-300'
                      : 'bg-gray-100 text-gray-600 border border-gray-300'
                  }`}
                >
                  📝 Éditeur
                </button>
                <button
                  onClick={() => setIsPaginatedMode(true)}
                  className={`px-3 py-1 text-sm rounded ${
                    isPaginatedMode
                      ? 'bg-blue-100 text-blue-700 border border-blue-300'
                      : 'bg-gray-100 text-gray-600 border border-gray-300'
                  }`}
                >
                  📄 Pages
                </button>
              </div>
            </div>

            {/* Éditeur selon le mode choisi */}
            <div className="flex-1">
              {isPaginatedMode ? (
                <PaginatedDocumentEditor
                  value={selectedDocument.content || ''}
                  onChange={handleContentChange}
                  documentType={selectedDocument.type_document!}
                  onTextSelection={handleTextSelection}
                  workspaceName="Mon Workspace" // TODO: récupérer depuis le contexte
                  workspaceLogo="" // TODO: récupérer depuis le contexte
                  onAddArticle={handleArticleSubmit}
                  onSave={handleSave}
                  onClose={handleClose}
                  hasUnsavedChanges={hasUnsavedChanges}
                  lotId={selectedLotId}
                  documentTitle={selectedDocument.name}
                />
              ) : (
                <ModernDocumentEditor
                  value={selectedDocument.content || ''}
                  onChange={handleContentChange}
                  documentType={selectedDocument.type_document!}
                  onTextSelection={handleTextSelection}
                  workspaceName="Mon Workspace" // TODO: récupérer depuis le contexte
                  workspaceLogo="" // TODO: récupérer depuis le contexte
                  onAddArticle={handleArticleSubmit}
                  onSave={handleSave}
                  onClose={handleClose}
                  hasUnsavedChanges={hasUnsavedChanges}
                  lotId={selectedLotId}
                />
              )}
            </div>
          </div>
        ) : (
          // Interface de création de document
          <div className="flex-1 flex items-center justify-center">
            <div className="max-w-md w-full mx-4">
              {!showCreateForm ? (
                // Écran d'accueil avec boutons de création
                <div className="text-center">
                  <div className="text-gray-400 mb-6">
                    <svg className="mx-auto h-16 w-16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    Créer un nouveau document
                  </h3>
                  <p className="text-gray-500 mb-8">
                    Choisissez le type de document technique à créer
                  </p>

                  <div className="space-y-4">
                    <button
                      onClick={() => handleStartCreate(DocumentType.CCTP)}
                      className="w-full flex items-center justify-center px-6 py-4 border border-transparent text-base font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
                    >
                      <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                      </svg>
                      Nouveau CCTP
                    </button>

                    <button
                      onClick={() => handleStartCreate(DocumentType.DPGF)}
                      className="w-full flex items-center justify-center px-6 py-4 border border-transparent text-base font-medium rounded-lg text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors"
                    >
                      <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                      </svg>
                      Nouveau DPGF
                    </button>
                  </div>

                  <div className="mt-8 pt-6 border-t border-gray-200">
                    <p className="text-sm text-gray-400">
                      Ou utilisez les paramètres d'URL pour charger un document existant
                    </p>
                    <p className="text-xs text-gray-400 mt-1">
                      Exemple: /documents-techniques?project_id=1&lot_id=1&document_id=5
                    </p>
                  </div>
                </div>
              ) : (
                // Formulaire de création
                <div className="bg-white rounded-lg shadow-lg p-6">
                  <div className="text-center mb-6">
                    <h3 className="text-lg font-semibold text-gray-900">
                      Nouveau document {newDocumentType}
                    </h3>
                    <p className="text-sm text-gray-500 mt-1">
                      Saisissez le nom du document à créer
                    </p>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <label htmlFor="document-name" className="block text-sm font-medium text-gray-700 mb-2">
                        Nom du document
                      </label>
                      <input
                        id="document-name"
                        type="text"
                        value={newDocumentName}
                        onChange={(e) => setNewDocumentName(e.target.value)}
                        placeholder="Nom du document"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        autoFocus
                      />
                    </div>

                    <div className="flex space-x-3 pt-4">
                      <button
                        onClick={() => setShowCreateForm(false)}
                        className="flex-1 px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                      >
                        Annuler
                      </button>
                      <button
                        onClick={handleCreateDocument}
                        disabled={!newDocumentName.trim() || isCreating}
                        className="flex-1 px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {isCreating ? 'Création...' : 'Créer'}
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

      {/* Indicateur de chargement global */}
      {enhancing && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 flex items-center space-x-3">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            <span>Amélioration du texte en cours...</span>
          </div>
        </div>
      )}
    </div>
  )
}

export default function DocumentsTechniquesPage() {
  return (
    <ProtectedRoute>
      <DocumentsTechniquesContent />
    </ProtectedRoute>
  )
}

'use client'

import React from 'react'
import { Editor } from '@tiptap/react'

interface PaginationToolbarProps {
  editor: Editor | null
  currentPage?: number
  totalPages?: number
  onPageChange?: (page: number) => void
}

export default function PaginationToolbar({
  editor,
  currentPage = 1,
  totalPages = 1,
  onPageChange
}: PaginationToolbarProps) {
  
  const insertPageBreak = () => {
    if (!editor) return
    editor.chain().focus().setPageBreak().run()
  }

  const goToPreviousPage = () => {
    if (currentPage > 1 && onPageChange) {
      onPageChange(currentPage - 1)
    }
  }

  const goToNextPage = () => {
    if (currentPage < totalPages && onPageChange) {
      onPageChange(currentPage + 1)
    }
  }

  if (!editor) return null

  return (
    <div className="pagination-toolbar flex items-center gap-3 p-2 bg-gray-50 border-b border-gray-200">
      {/* Bouton saut de page */}
      <button
        onClick={insertPageBreak}
        className="flex items-center gap-2 px-3 py-1.5 text-sm bg-white border border-gray-300 rounded hover:bg-gray-50 transition-colors"
        title="Insérer un saut de page (Ctrl+Entrée)"
      >
        <span>📄</span>
        <span>Saut de page</span>
      </button>

      {/* Séparateur */}
      <div className="w-px h-6 bg-gray-300"></div>

      {/* Navigation de pages */}
      <div className="flex items-center gap-2">
        <button
          onClick={goToPreviousPage}
          disabled={currentPage <= 1}
          className="px-2 py-1 text-sm bg-white border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          title="Page précédente"
        >
          ←
        </button>

        <span className="text-sm text-gray-600 min-w-[80px] text-center">
          Page {currentPage} / {totalPages}
        </span>

        <button
          onClick={goToNextPage}
          disabled={currentPage >= totalPages}
          className="px-2 py-1 text-sm bg-white border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          title="Page suivante"
        >
          →
        </button>
      </div>

      {/* Séparateur */}
      <div className="w-px h-6 bg-gray-300"></div>

      {/* Informations sur le document */}
      <div className="text-sm text-gray-500">
        Format A4 • {totalPages} page{totalPages > 1 ? 's' : ''}
      </div>
    </div>
  )
}

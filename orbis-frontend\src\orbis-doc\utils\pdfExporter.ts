import { OrbisDocContent, OrbisDocConfig } from '../types'

/**
 * Exporte un document en PDF en utilisant l'API d'impression du navigateur
 */
export async function exportToPDF(
  content: OrbisDocContent,
  config: OrbisDocConfig
): Promise<void> {
  try {
    // Créer une nouvelle fenêtre pour l'impression
    const printWindow = window.open('', '_blank')
    if (!printWindow) {
      throw new Error('Impossible d\'ouvrir la fenêtre d\'impression')
    }

    // Générer le HTML complet avec les styles d'impression
    const htmlContent = generatePrintHTML(content, config)
    
    printWindow.document.write(htmlContent)
    printWindow.document.close()
    
    // Attendre que le contenu soit chargé
    await new Promise((resolve) => {
      printWindow.onload = resolve
      // Fallback si onload ne se déclenche pas
      setTimeout(resolve, 1000)
    })
    
    printWindow.focus()
    printWindow.print()
    
    // Fermer la fenêtre après impression (optionnel)
    // printWindow.close()
    
  } catch (error) {
    console.error('Erreur lors de l\'export PDF:', error)
    throw new Error('Impossible d\'exporter le document en PDF')
  }
}

/**
 * Génère le HTML complet pour l'impression
 */
function generatePrintHTML(content: OrbisDocContent, config: OrbisDocConfig): string {
  const styles = generatePrintStyles(config)
  
  return `
    <!DOCTYPE html>
    <html lang="fr">
      <head>
        <meta charset="utf-8">
        <title>${config.documentTitle}</title>
        ${styles}
      </head>
      <body>
        <div class="document-container">
          ${content.html}
        </div>
      </body>
    </html>
  `
}

/**
 * Génère les styles CSS pour l'impression
 */
function generatePrintStyles(config: OrbisDocConfig): string {
  const pageSize = config.pageSize || 'A4'
  const margins = config.margins || { top: 25, right: 20, bottom: 25, left: 20 }
  
  return `
    <style>
      @page {
        size: ${pageSize};
        margin: ${margins.top}mm ${margins.right}mm ${margins.bottom}mm ${margins.left}mm;
      }
      
      * {
        box-sizing: border-box;
      }
      
      body {
        font-family: 'Times New Roman', serif;
        font-size: 12pt;
        line-height: 1.5;
        margin: 0;
        padding: 0;
        color: #000;
        background: white;
      }
      
      .document-container {
        width: 100%;
        max-width: none;
      }
      
      /* Titres */
      h1, h2, h3, h4, h5, h6 {
        font-family: 'Times New Roman', serif;
        font-weight: bold;
        margin-top: 1.5em;
        margin-bottom: 0.5em;
        page-break-after: avoid;
      }
      
      h1 { font-size: 18pt; }
      h2 { font-size: 16pt; }
      h3 { font-size: 14pt; }
      h4 { font-size: 12pt; }
      h5 { font-size: 11pt; }
      h6 { font-size: 10pt; }
      
      /* Paragraphes */
      p {
        margin-bottom: 1em;
        text-align: justify;
        orphans: 2;
        widows: 2;
      }
      
      /* Listes */
      ul, ol {
        margin-bottom: 1em;
        padding-left: 1.5em;
      }
      
      li {
        margin-bottom: 0.25em;
      }
      
      /* Tableaux */
      table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 1em;
        page-break-inside: avoid;
      }
      
      th, td {
        border: 1px solid #000;
        padding: 0.5em;
        text-align: left;
        vertical-align: top;
      }
      
      th {
        background-color: #f0f0f0;
        font-weight: bold;
      }
      
      /* Images */
      img {
        max-width: 100%;
        height: auto;
        page-break-inside: avoid;
      }
      
      /* Page de garde */
      .cover-page {
        page-break-after: always;
        text-align: center;
        padding: 2cm 0;
      }
      
      .cover-page-title {
        font-size: 24pt;
        font-weight: bold;
        margin-bottom: 1em;
      }
      
      .cover-page-subtitle {
        font-size: 18pt;
        margin-bottom: 2em;
        font-style: italic;
      }
      
      /* Sauts de page */
      .page-break {
        page-break-before: always;
      }
      
      /* Éviter les sauts de page indésirables */
      .no-break {
        page-break-inside: avoid;
      }
      
      /* Styles spécifiques aux templates */
      .cctp-template .classification-label {
        font-size: 10pt;
        font-weight: bold;
        text-transform: uppercase;
      }
      
      .cctp-template .classification-type {
        font-size: 20pt;
        font-weight: bold;
        margin: 0.5em 0;
      }
      
      .dpgf-template .meta-table {
        margin: 2em auto;
        max-width: 500px;
      }
      
      .rapport-template .document-type-badge {
        display: inline-block;
        background: #000;
        color: white;
        padding: 0.5em 1em;
        margin-bottom: 1em;
        font-weight: bold;
      }
      
      /* Masquer les éléments non imprimables */
      .no-print,
      .toolbar,
      .page-navigator,
      button,
      .hover-only {
        display: none !important;
      }
      
      /* Optimisations pour l'impression */
      @media print {
        body {
          -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
        }
        
        .page-break {
          page-break-before: always;
        }
        
        .cover-page {
          page-break-after: always;
        }
        
        h1, h2, h3 {
          page-break-after: avoid;
        }
        
        table, figure, img {
          page-break-inside: avoid;
        }
        
        p, li {
          orphans: 2;
          widows: 2;
        }
      }
    </style>
  `
}

/**
 * Prépare le contenu pour l'impression en nettoyant le HTML
 */
export function prepareContentForPrint(html: string): string {
  // Supprimer les éléments non imprimables
  let cleanHtml = html
    .replace(/<button[^>]*>.*?<\/button>/gi, '')
    .replace(/<div[^>]*class="[^"]*no-print[^"]*"[^>]*>.*?<\/div>/gi, '')
    .replace(/<span[^>]*class="[^"]*hover-only[^"]*"[^>]*>.*?<\/span>/gi, '')
  
  // Convertir les sauts de page personnalisés
  cleanHtml = cleanHtml.replace(
    /<div[^>]*data-type="page-break"[^>]*>.*?<\/div>/gi,
    '<div class="page-break"></div>'
  )
  
  return cleanHtml
}

/**
 * Estime le nombre de pages pour l'impression
 */
export function estimatePrintPages(
  content: OrbisDocContent,
  config: OrbisDocConfig
): number {
  // Estimation basique basée sur le nombre de caractères
  const avgCharsPerPage = 2000 // Estimation pour une page A4
  const charCount = content.characterCount || content.html.length
  
  return Math.max(1, Math.ceil(charCount / avgCharsPerPage))
}

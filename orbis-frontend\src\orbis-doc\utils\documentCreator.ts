import { OrbisDocConfig, OrbisDoc<PERSON>ontent, OrbisDocCoverPage } from '../types'

/**
 * Crée un nouveau document vide avec la configuration spécifiée
 */
export function createDocument(config: OrbisDocConfig): OrbisDocContent {
  const baseContent: OrbisDocContent = {
    html: '',
    json: null,
    pages: [],
    totalPages: 1,
    wordCount: 0,
    characterCount: 0
  }

  // Ajouter une page de garde si configurée
  if (config.coverPage) {
    baseContent.coverPage = config.coverPage
    baseContent.html = generateInitialContent(config)
  }

  return baseContent
}

/**
 * Génère le contenu HTML initial selon le type de document
 */
function generateInitialContent(config: OrbisDocConfig): string {
  const { documentType } = config

  switch (documentType) {
    case 'CCTP':
      return generateCCTPTemplate(config)
    case 'DPGF':
      return generateDPGFTemplate(config)
    case 'RAPPORT':
      return generateRapportTemplate(config)
    default:
      return generateDefaultTemplate(config)
  }
}

/**
 * Template pour CCTP
 */
function generateCCTPTemplate(config: OrbisDocConfig): string {
  return `
    <h1>1. OBJET DU MARCHÉ</h1>
    <p>Le présent Cahier des Clauses Techniques Particulières (CCTP) a pour objet de définir les prestations techniques à réaliser dans le cadre de ${config.documentTitle}.</p>
    
    <h1>2. DESCRIPTION DES TRAVAUX</h1>
    <p>Les travaux comprennent :</p>
    <ul>
      <li>Description des prestations</li>
      <li>Matériaux et équipements</li>
      <li>Mise en œuvre</li>
    </ul>
    
    <h1>3. PRESCRIPTIONS TECHNIQUES</h1>
    <h2>3.1. Matériaux</h2>
    <p>Tous les matériaux utilisés devront être conformes aux normes en vigueur.</p>
    
    <h2>3.2. Mise en œuvre</h2>
    <p>La mise en œuvre devra respecter les règles de l'art.</p>
    
    <h1>4. CONTRÔLES ET RÉCEPTION</h1>
    <p>Les contrôles seront effectués selon les modalités définies dans le présent CCTP.</p>
  `
}

/**
 * Template pour DPGF
 */
function generateDPGFTemplate(config: OrbisDocConfig): string {
  return `
    <h1>DÉTAIL DES PRIX GLOBAL ET FORFAITAIRE</h1>
    
    <h2>LOT 1 - GROS ŒUVRE</h2>
    <table>
      <thead>
        <tr>
          <th>Désignation</th>
          <th>Unité</th>
          <th>Quantité</th>
          <th>Prix unitaire HT</th>
          <th>Prix total HT</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>Terrassement</td>
          <td>m³</td>
          <td>100</td>
          <td>25,00 €</td>
          <td>2 500,00 €</td>
        </tr>
        <tr>
          <td>Béton de fondation</td>
          <td>m³</td>
          <td>50</td>
          <td>120,00 €</td>
          <td>6 000,00 €</td>
        </tr>
      </tbody>
    </table>
    
    <h2>LOT 2 - SECOND ŒUVRE</h2>
    <table>
      <thead>
        <tr>
          <th>Désignation</th>
          <th>Unité</th>
          <th>Quantité</th>
          <th>Prix unitaire HT</th>
          <th>Prix total HT</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>Cloisons</td>
          <td>m²</td>
          <td>200</td>
          <td>45,00 €</td>
          <td>9 000,00 €</td>
        </tr>
      </tbody>
    </table>
    
    <h2>RÉCAPITULATIF</h2>
    <p><strong>Total HT : 17 500,00 €</strong></p>
    <p><strong>TVA 20% : 3 500,00 €</strong></p>
    <p><strong>Total TTC : 21 000,00 €</strong></p>
  `
}

/**
 * Template pour rapport
 */
function generateRapportTemplate(config: OrbisDocConfig): string {
  return `
    <h1>RÉSUMÉ EXÉCUTIF</h1>
    <p>Ce rapport présente les résultats de l'étude menée dans le cadre de ${config.documentTitle}.</p>
    
    <h1>1. INTRODUCTION</h1>
    <h2>1.1. Contexte</h2>
    <p>Description du contexte de l'étude.</p>
    
    <h2>1.2. Objectifs</h2>
    <p>Les objectifs de cette étude sont :</p>
    <ul>
      <li>Objectif 1</li>
      <li>Objectif 2</li>
      <li>Objectif 3</li>
    </ul>
    
    <h1>2. MÉTHODOLOGIE</h1>
    <p>Description de la méthodologie employée.</p>
    
    <h1>3. RÉSULTATS</h1>
    <p>Présentation des résultats obtenus.</p>
    
    <h1>4. CONCLUSIONS ET RECOMMANDATIONS</h1>
    <p>Conclusions de l'étude et recommandations.</p>
  `
}

/**
 * Template par défaut
 */
function generateDefaultTemplate(config: OrbisDocConfig): string {
  return `
    <h1>Introduction</h1>
    <p>Bienvenue dans votre nouveau document ${config.documentType}.</p>
    <p>Vous pouvez commencer à rédiger votre contenu ici.</p>
    
    <h2>Section 1</h2>
    <p>Contenu de la première section.</p>
    
    <h2>Section 2</h2>
    <p>Contenu de la deuxième section.</p>
  `
}

/**
 * Crée un document à partir d'un template existant
 */
export function createFromTemplate(
  templateName: string,
  config: OrbisDocConfig
): OrbisDocContent {
  const document = createDocument(config)
  
  // Charger le template spécifique
  switch (templateName) {
    case 'cctp-standard':
      document.html = generateCCTPTemplate(config)
      break
    case 'dpgf-standard':
      document.html = generateDPGFTemplate(config)
      break
    case 'rapport-technique':
      document.html = generateRapportTemplate(config)
      break
    default:
      document.html = generateDefaultTemplate(config)
  }
  
  return document
}

/**
 * Clone un document existant
 */
export function cloneDocument(
  originalDocument: OrbisDocContent,
  newConfig: Partial<OrbisDocConfig>
): OrbisDocContent {
  return {
    ...originalDocument,
    // Réinitialiser certaines propriétés
    pages: [],
    totalPages: 1,
    // Mettre à jour la page de garde si nécessaire
    coverPage: newConfig.coverPage || originalDocument.coverPage
  }
}

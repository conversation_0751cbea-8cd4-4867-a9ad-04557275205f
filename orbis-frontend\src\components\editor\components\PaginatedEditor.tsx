'use client'

import React, { useEffect } from 'react'
import { EditorContent } from '@tiptap/react'
import { usePagination } from '../hooks/usePagination'
import { Editor } from '@tiptap/react'
import '../styles/pagination.css'

interface PaginatedEditorProps {
  editor: Editor | null
  documentTitle?: string
  className?: string
}

export default function PaginatedEditor({
  editor,
  documentTitle = 'Document CCTP',
  className = ''
}: PaginatedEditorProps) {
  const {
    pages,
    currentPage,
    containerRef,
    generateFooter,
    insertPageBreak,
    updatePagination
  } = usePagination({
    editor,
    documentTitle
  })

  // Fonction pour faire défiler vers une page spécifique
  const scrollToPage = (pageNumber: number) => {
    if (!containerRef.current) return

    const pageHeight = 1123 + 20 // Hauteur page + marge
    const targetY = (pageNumber - 1) * pageHeight

    containerRef.current.scrollTo({
      top: targetY,
      behavior: 'smooth'
    })
  }

  // Vérifier s'il y a une page de garde dans le contenu
  const hasCoverPage = () => {
    if (!editor) return false
    const doc = editor.state.doc
    let foundCoverPage = false

    doc.descendants((node) => {
      if (node.type.name === 'coverPage') {
        foundCoverPage = true
        return false // Arrêter la recherche
      }
    })

    return foundCoverPage
  }

  // Générer les pages avec pieds de page
  const renderPages = () => {
    const hasCP = hasCoverPage()

    return (
      <>
        {/* Page de garde (si présente) - ne compte pas dans la numérotation */}
        {hasCP && (
          <div className="document-page cover-page-wrapper">
            <div className="page-content">
              {/* La page de garde sera rendue par l'éditeur */}
            </div>
            {/* Pas de pied de page pour la page de garde */}
          </div>
        )}

        {/* Page vierge après la page de garde (si page de garde présente) */}
        {hasCP && (
          <div className="document-page blank-page">
            <div className="page-content">
              {/* Page intentionnellement vierge */}
            </div>
            {/* Pas de pied de page pour la page vierge */}
          </div>
        )}

        {/* Pages de contenu principal */}
        {pages.map((pageNumber) => (
          <div key={`content-${pageNumber}`} className="document-page">
            <div className="page-content">
              {pageNumber === 1 && !hasCP && (
                <EditorContent editor={editor} />
              )}
              {pageNumber === 1 && hasCP && (
                <EditorContent editor={editor} />
              )}
            </div>
            <div
              className="page-footer"
              dangerouslySetInnerHTML={{ __html: generateFooter(pageNumber, false, false) }}
            />
          </div>
        ))}
      </>
    )
  }

  if (!editor) {
    return (
      <div className="paginated-editor">
        <div className="loading-editor">
          <p>Chargement de l'éditeur...</p>
        </div>
      </div>
    )
  }

  return (
    <div className={`paginated-editor ${className}`}>
      {/* Barre d'outils de pagination */}
      <div className="pagination-toolbar">
        <button
          onClick={insertPageBreak}
          className="page-break-button"
          title="Insérer un saut de page (Ctrl+Entrée)"
        >
          📄 Saut de page
        </button>
        
        <div className="page-navigation">
          <button
            onClick={() => scrollToPage(Math.max(1, currentPage - 1))}
            disabled={currentPage <= 1}
            className="nav-button"
          >
            ← Page précédente
          </button>
          
          <span className="page-info">
            Page {currentPage} sur {pages.length}
          </span>
          
          <button
            onClick={() => scrollToPage(Math.min(pages.length, currentPage + 1))}
            disabled={currentPage >= pages.length}
            className="nav-button"
          >
            Page suivante →
          </button>
        </div>
      </div>

      {/* Zone de contenu paginé */}
      <div
        ref={containerRef}
        className="paginated-content"
      >
        {/* Rendu des pages avec gestion de la page de garde */}
        {renderPages()}
      </div>

      {/* Indicateur de page courante */}
      <div className="page-indicator">
        Page {currentPage} / {pages.length}
      </div>
    </div>
  )
}

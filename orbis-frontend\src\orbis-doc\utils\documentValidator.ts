import { OrbisDocContent, OrbisDocConfig } from '../types'

export interface ValidationResult {
  isValid: boolean
  errors: ValidationError[]
  warnings: ValidationWarning[]
}

export interface ValidationError {
  type: 'error'
  code: string
  message: string
  location?: string
}

export interface ValidationWarning {
  type: 'warning'
  code: string
  message: string
  location?: string
}

/**
 * Valide un document complet
 */
export function validateDocument(
  content: OrbisDocContent,
  config: OrbisDocConfig
): ValidationResult {
  const errors: ValidationError[] = []
  const warnings: ValidationWarning[] = []

  // Validation du contenu
  const contentValidation = validateContent(content)
  errors.push(...contentValidation.errors)
  warnings.push(...contentValidation.warnings)

  // Validation de la configuration
  const configValidation = validateConfig(config)
  errors.push(...configValidation.errors)
  warnings.push(...configValidation.warnings)

  // Validation de la structure
  const structureValidation = validateStructure(content, config)
  errors.push(...structureValidation.errors)
  warnings.push(...structureValidation.warnings)

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  }
}

/**
 * Valide le contenu du document
 */
function validateContent(content: OrbisDocContent): ValidationResult {
  const errors: ValidationError[] = []
  const warnings: ValidationWarning[] = []

  // Vérifier que le contenu n'est pas vide
  if (!content.html || content.html.trim().length === 0) {
    errors.push({
      type: 'error',
      code: 'EMPTY_CONTENT',
      message: 'Le document ne peut pas être vide'
    })
  }

  // Vérifier la longueur minimale
  if (content.html && content.html.trim().length < 50) {
    warnings.push({
      type: 'warning',
      code: 'SHORT_CONTENT',
      message: 'Le document semble très court'
    })
  }

  // Vérifier la présence de titres
  const hasHeadings = /<h[1-6][^>]*>/i.test(content.html)
  if (!hasHeadings) {
    warnings.push({
      type: 'warning',
      code: 'NO_HEADINGS',
      message: 'Le document ne contient aucun titre'
    })
  }

  // Vérifier les images cassées
  const brokenImages = content.html.match(/<img[^>]*src="[^"]*"[^>]*>/gi)
  if (brokenImages) {
    brokenImages.forEach((img, index) => {
      const srcMatch = img.match(/src="([^"]*)"/)
      if (srcMatch && srcMatch[1].startsWith('data:')) {
        // Image en base64, OK
      } else if (srcMatch && !srcMatch[1].startsWith('http')) {
        warnings.push({
          type: 'warning',
          code: 'RELATIVE_IMAGE_PATH',
          message: `Image ${index + 1} utilise un chemin relatif qui pourrait ne pas fonctionner à l'export`,
          location: `Image: ${srcMatch[1]}`
        })
      }
    })
  }

  return { isValid: errors.length === 0, errors, warnings }
}

/**
 * Valide la configuration du document
 */
function validateConfig(config: OrbisDocConfig): ValidationResult {
  const errors: ValidationError[] = []
  const warnings: ValidationWarning[] = []

  // Vérifier le titre du document
  if (!config.documentTitle || config.documentTitle.trim().length === 0) {
    errors.push({
      type: 'error',
      code: 'MISSING_TITLE',
      message: 'Le titre du document est obligatoire'
    })
  }

  // Vérifier le type de document
  if (!config.documentType) {
    errors.push({
      type: 'error',
      code: 'MISSING_DOCUMENT_TYPE',
      message: 'Le type de document est obligatoire'
    })
  }

  // Vérifier les marges
  if (config.margins) {
    const { top, right, bottom, left } = config.margins
    if (top < 10 || right < 10 || bottom < 10 || left < 10) {
      warnings.push({
        type: 'warning',
        code: 'SMALL_MARGINS',
        message: 'Les marges sont très petites (< 10mm)'
      })
    }
    if (top > 50 || right > 50 || bottom > 50 || left > 50) {
      warnings.push({
        type: 'warning',
        code: 'LARGE_MARGINS',
        message: 'Les marges sont très grandes (> 50mm)'
      })
    }
  }

  // Vérifier l'auto-sauvegarde
  if (config.autoSave && config.autoSaveInterval && config.autoSaveInterval < 10000) {
    warnings.push({
      type: 'warning',
      code: 'FREQUENT_AUTOSAVE',
      message: 'L\'intervalle d\'auto-sauvegarde est très court (< 10s)'
    })
  }

  return { isValid: errors.length === 0, errors, warnings }
}

/**
 * Valide la structure du document
 */
function validateStructure(content: OrbisDocContent, config: OrbisDocConfig): ValidationResult {
  const errors: ValidationError[] = []
  const warnings: ValidationWarning[] = []

  // Vérifier la hiérarchie des titres
  const headingValidation = validateHeadingHierarchy(content.html)
  errors.push(...headingValidation.errors)
  warnings.push(...headingValidation.warnings)

  // Vérifier les tableaux
  const tableValidation = validateTables(content.html)
  warnings.push(...tableValidation.warnings)

  // Vérifier la page de garde
  if (config.coverPage) {
    const coverPageValidation = validateCoverPage(config.coverPage)
    warnings.push(...coverPageValidation.warnings)
  }

  return { isValid: errors.length === 0, errors, warnings }
}

/**
 * Valide la hiérarchie des titres
 */
function validateHeadingHierarchy(html: string): ValidationResult {
  const errors: ValidationError[] = []
  const warnings: ValidationWarning[] = []

  const headings = html.match(/<h([1-6])[^>]*>(.*?)<\/h[1-6]>/gi)
  if (!headings) return { isValid: true, errors, warnings }

  let previousLevel = 0
  headings.forEach((heading, index) => {
    const levelMatch = heading.match(/<h([1-6])/)
    if (levelMatch) {
      const currentLevel = parseInt(levelMatch[1])
      
      if (currentLevel > previousLevel + 1) {
        warnings.push({
          type: 'warning',
          code: 'HEADING_HIERARCHY_SKIP',
          message: `Saut de niveau de titre détecté (H${previousLevel} vers H${currentLevel})`,
          location: `Titre ${index + 1}`
        })
      }
      
      previousLevel = currentLevel
    }
  })

  return { isValid: errors.length === 0, errors, warnings }
}

/**
 * Valide les tableaux
 */
function validateTables(html: string): ValidationResult {
  const warnings: ValidationWarning[] = []

  const tables = html.match(/<table[^>]*>[\s\S]*?<\/table>/gi)
  if (!tables) return { isValid: true, errors: [], warnings }

  tables.forEach((table, index) => {
    // Vérifier la présence d'en-têtes
    if (!/<th[^>]*>/i.test(table)) {
      warnings.push({
        type: 'warning',
        code: 'TABLE_NO_HEADERS',
        message: `Le tableau ${index + 1} n'a pas d'en-têtes`,
        location: `Tableau ${index + 1}`
      })
    }

    // Vérifier les cellules vides
    const emptyCells = (table.match(/<td[^>]*>\s*<\/td>/gi) || []).length
    if (emptyCells > 0) {
      warnings.push({
        type: 'warning',
        code: 'TABLE_EMPTY_CELLS',
        message: `Le tableau ${index + 1} contient ${emptyCells} cellule(s) vide(s)`,
        location: `Tableau ${index + 1}`
      })
    }
  })

  return { isValid: true, errors: [], warnings }
}

/**
 * Valide la page de garde
 */
function validateCoverPage(coverPage: any): ValidationResult {
  const warnings: ValidationWarning[] = []

  if (!coverPage.title || coverPage.title.trim().length === 0) {
    warnings.push({
      type: 'warning',
      code: 'COVER_PAGE_NO_TITLE',
      message: 'La page de garde n\'a pas de titre'
    })
  }

  if (!coverPage.date) {
    warnings.push({
      type: 'warning',
      code: 'COVER_PAGE_NO_DATE',
      message: 'La page de garde n\'a pas de date'
    })
  }

  return { isValid: true, errors: [], warnings }
}

/**
 * Valide le HTML pour détecter les erreurs de structure
 */
export function validateHTML(html: string): ValidationResult {
  const errors: ValidationError[] = []
  const warnings: ValidationWarning[] = []

  // Vérifier les balises non fermées (basique)
  const openTags = html.match(/<[^\/][^>]*>/g) || []
  const closeTags = html.match(/<\/[^>]*>/g) || []
  
  if (openTags.length !== closeTags.length) {
    warnings.push({
      type: 'warning',
      code: 'HTML_STRUCTURE',
      message: 'Possible problème de structure HTML (balises non fermées)'
    })
  }

  // Vérifier les attributs dangereux
  if (/javascript:/i.test(html)) {
    errors.push({
      type: 'error',
      code: 'DANGEROUS_CONTENT',
      message: 'Contenu JavaScript détecté dans le HTML'
    })
  }

  return { isValid: errors.length === 0, errors, warnings }
}

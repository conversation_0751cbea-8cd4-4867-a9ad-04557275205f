/* Styles pour l'éditeur Tiptap */

.ProseMirror {
  outline: none;
  min-height: 400px;
  padding: 1rem;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  font-size: 14px;
  line-height: 1.6;
  color: #333;
}

.ProseMirror h1,
.ProseMirror h2,
.ProseMirror h3,
.ProseMirror h4,
.ProseMirror h5,
.ProseMirror h6 {
  color: #0F766E;
  margin-top: 1.5em;
  margin-bottom: 0.5em;
  font-weight: 600;
}

.ProseMirror h1 {
  font-size: 24px;
  border-bottom: 2px solid #0F766E;
  padding-bottom: 8px;
}

.ProseMirror h2 {
  font-size: 20px;
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 4px;
}

.ProseMirror h3 {
  font-size: 18px;
}

.ProseMirror h4 {
  font-size: 16px;
}

.ProseMirror h5 {
  font-size: 14px;
}

.ProseMirror h6 {
  font-size: 12px;
}

.ProseMirror p {
  margin: 0.5em 0;
}

.ProseMirror ul,
.ProseMirror ol {
  margin: 0.5em 0;
  padding-left: 1.5em;
}

.ProseMirror li {
  margin: 0.25em 0;
}

.ProseMirror ul li {
  list-style-type: disc;
}

.ProseMirror ol li {
  list-style-type: decimal;
}

.ProseMirror blockquote {
  border-left: 4px solid #0F766E;
  margin: 1em 0;
  padding-left: 1em;
  font-style: italic;
  background-color: #f8f9fa;
}

.ProseMirror code {
  background-color: #f1f5f9;
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 0.9em;
}

.ProseMirror pre {
  background-color: #f1f5f9;
  padding: 1em;
  border-radius: 6px;
  overflow-x: auto;
  margin: 1em 0;
}

.ProseMirror pre code {
  background: none;
  padding: 0;
}

.ProseMirror table {
  border-collapse: collapse;
  margin: 1em 0;
  width: 100%;
}

.ProseMirror table td,
.ProseMirror table th {
  border: 1px solid #e5e7eb;
  padding: 0.5em;
  text-align: left;
}

.ProseMirror table th {
  background-color: #f8f9fa;
  font-weight: 600;
}

.ProseMirror table tr:nth-child(even) {
  background-color: #f8f9fa;
}

.ProseMirror a {
  color: #0F766E;
  text-decoration: underline;
}

.ProseMirror a:hover {
  color: #065f46;
}

.ProseMirror mark {
  background-color: #fef08a;
  padding: 0.1em 0.2em;
  border-radius: 2px;
}

/* Classes spécifiques pour les listes Tiptap */
.tiptap-bullet-list {
  list-style-type: disc;
}

.tiptap-ordered-list {
  list-style-type: decimal;
}

/* Styles pour les éléments sélectionnés */
.ProseMirror-selectednode {
  outline: 2px solid #0F766E;
  outline-offset: 2px;
}

/* Styles pour le placeholder */
.ProseMirror p.is-editor-empty:first-child::before {
  content: attr(data-placeholder);
  float: left;
  color: #9ca3af;
  pointer-events: none;
  height: 0;
}

/* Styles pour les éléments de focus */
.ProseMirror:focus {
  outline: none;
}

/* Styles pour les sauts de page */
.ProseMirror .page-break {
  height: 1px;
  background: transparent;
  border: none;
  page-break-before: always;
  margin: 20px 0;
  padding: 0;
  position: relative;
  border-top: 1px dashed #ccc;
}

.ProseMirror .page-break::before {
  content: "--- Saut de page ---";
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  background: white;
  padding: 2px 8px;
  font-size: 10px;
  color: #666;
  border: 1px dashed #ccc;
  border-radius: 3px;
  white-space: nowrap;
}

/* Styles pour les tableaux redimensionnables */
.tableWrapper {
  overflow-x: auto;
}

.resize-cursor {
  cursor: ew-resize;
  cursor: col-resize;
}

/* Styles pour les menus contextuels */
.tiptap-context-menu {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  padding: 0.5rem 0;
  z-index: 1000;
}

.tiptap-context-menu-item {
  display: block;
  width: 100%;
  padding: 0.5rem 1rem;
  text-align: left;
  border: none;
  background: none;
  cursor: pointer;
  font-size: 14px;
  color: #374151;
}

.tiptap-context-menu-item:hover {
  background-color: #f3f4f6;
  color: #0F766E;
}

.tiptap-context-menu-item:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.tiptap-context-menu-separator {
  height: 1px;
  background-color: #e5e7eb;
  margin: 0.25rem 0;
}

/* Styles pour les boutons de la toolbar */
.tiptap-toolbar {
  border-bottom: 1px solid #e5e7eb;
  padding: 0.75rem;
  background-color: #f8f9fa;
  border-radius: 6px 6px 0 0;
}

.tiptap-toolbar-group {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding-right: 0.75rem;
  margin-right: 0.75rem;
  border-right: 1px solid #d1d5db;
}

.tiptap-toolbar-group:last-child {
  border-right: none;
  margin-right: 0;
  padding-right: 0;
}

.tiptap-toolbar-button {
  padding: 0.5rem;
  border: none;
  background: none;
  cursor: pointer;
  border-radius: 4px;
  color: #6b7280;
  transition: all 0.2s;
}

.tiptap-toolbar-button:hover {
  background-color: #e5e7eb;
  color: #374151;
}

.tiptap-toolbar-button.is-active {
  background-color: #0F766E;
  color: white;
}

.tiptap-toolbar-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Styles pour les sélecteurs */
.tiptap-select {
  padding: 0.25rem 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  background: white;
  font-size: 14px;
  color: #374151;
}

.tiptap-select:focus {
  outline: none;
  border-color: #0F766E;
  box-shadow: 0 0 0 1px #0F766E;
}

/* Animation pour le chargement */
.tiptap-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 24rem;
  background-color: #f9fafb;
  border-radius: 6px;
}

/* Styles pour l'impression */
@media print {
  .ProseMirror {
    font-size: 12pt;
    line-height: 1.5;
    color: #000;
  }

  .ProseMirror h1,
  .ProseMirror h2,
  .ProseMirror h3,
  .ProseMirror h4,
  .ProseMirror h5,
  .ProseMirror h6 {
    color: #000;
    page-break-after: avoid;
  }

  .ProseMirror table {
    page-break-inside: avoid;
  }

  .ProseMirror .page-break::before {
    display: none;
  }
}

.tiptap-loading-spinner {
  animation: spin 1s linear infinite;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #0F766E;
  border-radius: 50%;
  width: 24px;
  height: 24px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Styles pour le menu flottant */
.floating-menu {
  z-index: 10;
}

.floating-menu button {
  transition: all 0.2s ease;
}

.floating-menu button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

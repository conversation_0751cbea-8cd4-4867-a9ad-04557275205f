/* Styles pour le custom node CoverPage */

.cover-page-wrapper {
  position: relative;
  margin: 20px 0;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
}

.cover-page-wrapper.ProseMirror-selectednode {
  border-color: #0F766E;
  box-shadow: 0 0 0 2px rgba(15, 118, 110, 0.2);
}

.cover-page-selected-indicator {
  position: absolute;
  top: -30px;
  left: 0;
  background: #0F766E;
  color: white;
  padding: 4px 8px;
  font-size: 12px;
  border-radius: 4px 4px 0 0;
  z-index: 10;
}

.cover-page-container {
  position: relative;
  background: white;
}

.cover-page-content {
  pointer-events: none;
  user-select: none;
}

.cover-page-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s;
}

.cover-page-wrapper:hover .cover-page-overlay {
  opacity: 1;
  background: rgba(0, 0, 0, 0.1);
}

.cover-page-controls {
  display: flex;
  gap: 8px;
}

.cover-page-button {
  background: #0F766E;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: background 0.2s;
}

.cover-page-button:hover {
  background: #0d5d56;
}

/* Styles pour le contenu de la page de garde HARMONIA */
.cover-page-content .cover-page {
  margin: 0;
  border: 2px solid #333;
  width: 100%;
  min-height: 400px;
  box-sizing: border-box;
  transform: scale(0.6);
  transform-origin: top left;
  width: 166.67%;
  font-family: Arial, sans-serif;
  background-color: white;
  display: flex;
  overflow: hidden;
}

/* Colonne gauche - Entreprises HARMONIA */
.cover-page-content .left-column {
  width: 40%;
  background-color: #e8e8e8;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.cover-page-content .entreprise-block {
  background-color: white;
  border: 1px solid #ccc;
  padding: 8px;
  border-radius: 4px;
}

.cover-page-content .entreprise-header {
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.cover-page-content .logo-container {
  width: 50px;
  height: 50px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #ddd;
  background-color: white;
}

.cover-page-content .company-logo {
  max-width: 48px;
  max-height: 48px;
  object-fit: contain;
}

.cover-page-content .logo-placeholder {
  width: 100%;
  height: 100%;
  background-color: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  color: #666;
  font-weight: bold;
}

.cover-page-content .company-info {
  flex: 1;
  font-size: 9px;
  line-height: 1.3;
}

.cover-page-content .role-label {
  font-weight: bold;
  color: #333;
  margin-bottom: 2px;
  font-size: 8px;
}

.cover-page-content .company-name {
  font-weight: bold;
  color: #000;
  margin-bottom: 2px;
  font-size: 9px;
}

.cover-page-content .company-activity,
.cover-page-content .company-address,
.cover-page-content .company-contact {
  color: #666;
  font-size: 8px;
  margin-bottom: 1px;
}

/* Colonne droite - Projet HARMONIA */
.cover-page-content .right-column {
  width: 60%;
  background-color: white;
  padding: 15px;
  display: flex;
  flex-direction: column;
  position: relative;
}

.cover-page-content .header-logos {
  position: absolute;
  top: 10px;
  right: 15px;
  display: flex;
  gap: 5px;
}

.cover-page-content .header-logo {
  height: 30px;
  width: auto;
  object-fit: contain;
}

.cover-page-content .project-title-section {
  text-align: center;
  margin-top: 60px;
  margin-bottom: 20px;
}

.cover-page-content .project-main-title {
  font-size: 14px;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
}

.cover-page-content .project-address {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

.cover-page-content .project-image {
  max-width: 100%;
  max-height: 120px;
  object-fit: cover;
  border: 1px solid #ccc;
}

.cover-page-content .moa-section {
  border: 2px solid #333;
  margin-bottom: 15px;
  background-color: #f9f9f9;
}

.cover-page-content .moa-header {
  background-color: #333;
  color: white;
  text-align: center;
  padding: 5px;
  font-size: 11px;
  font-weight: bold;
}

.cover-page-content .moa-content {
  padding: 8px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.cover-page-content .moa-logo img {
  height: 40px;
  width: auto;
  object-fit: contain;
}

.cover-page-content .moa-name {
  font-weight: bold;
  font-size: 11px;
  margin-bottom: 2px;
}

.cover-page-content .moa-address,
.cover-page-content .moa-contact {
  font-size: 9px;
  color: #666;
}

.cover-page-content .lot-section {
  text-align: center;
  margin-bottom: 15px;
}

.cover-page-content .lot-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
}

.cover-page-content .lot-description {
  font-size: 12px;
  font-weight: bold;
  text-transform: uppercase;
  color: #666;
  letter-spacing: 0.5px;
}

.cover-page-content .document-section {
  text-align: center;
  margin-bottom: 20px;
}

.cover-page-content .document-title {
  background-color: #e8e8e8;
  border: 2px solid #333;
  border-radius: 10px;
  padding: 8px;
  font-size: 12px;
  font-weight: bold;
  color: #333;
}

/* Tableau d'informations HARMONIA */
.cover-page-content .info-section {
  margin-top: auto;
  margin-bottom: 20px;
}

.cover-page-content .info-table {
  width: 100%;
  border-collapse: collapse;
  border: 2px solid #333;
  font-size: 10px;
}

.cover-page-content .info-label {
  background-color: #e8e8e8;
  border: 1px solid #333;
  padding: 4px 8px;
  font-weight: bold;
  width: 40%;
}

.cover-page-content .info-value {
  border: 1px solid #333;
  padding: 4px 8px;
  text-align: center;
  font-weight: bold;
}

/* Styles supplémentaires pour HARMONIA */
.cover-page-content .project-image-section {
  text-align: center;
  margin-bottom: 20px;
}
